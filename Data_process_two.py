import numpy as np
import csv

#数据载入、分割、标幺化
def DataPre(granularity,excel_file):
    with open(excel_file) as file:                
        reader = csv.reader(file)               
        rows = [row for row in reader]                                  
    data =np.array(rows,dtype=np.float32)                                                
    dim=int(1440/granularity) #日出力曲线数据维度
    data=np.reshape(data,(-1,dim))
    return data


#数据处理主函数
def PowerDataProcess(granularity,path_array):
    #参数设置
    data=DataPre(granularity,path_array[0])
    if np.size(path_array,0)>1:
        for i in range (1,np.size(path_array,0)):
            path=path_array[i]
            data=np.vstack((data,DataPre(granularity,path)))
 
    dim=int(1440/granularity) #日出力曲线数据维度
    data=np.reshape(data,(-1,dim))
    num_sample=np.size(data,0)
    
    return data,dim,num_sample

def WeatherDataProcess(granularity,path_from_array):

    weather_factor=DataPre(granularity,path_from_array[0])
    if np.size(path_from_array,0)>1:
        for i in range (1,np.size(path_from_array,0)):
            path=path_from_array[i]
            weather_factor=np.vstack((weather_factor,DataPre(granularity,path)))
 
    dim=int(1440/granularity) #气象曲线数据维度
    weather_factor=np.reshape(weather_factor,(-1,dim))
    num_sample=np.size(weather_factor,0)
    
    return weather_factor,dim,num_sample



