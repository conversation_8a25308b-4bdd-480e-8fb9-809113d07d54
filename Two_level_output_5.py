# -*- coding: utf-8 -*-
"""
光伏出力二级输出模块
用于基于用户指定天气类型的光伏功率预测和不确定性评估
"""

# 导入必要的库
import os                         # 操作系统接口
import pandas as pd               # 数据处理库
import numpy as np                # 数值计算库
import pickle                     # 序列化库
import torch                      # PyTorch深度学习框架
import matplotlib.pyplot as plt   # 绘图库

# 导入自定义模块
from Bayes_bp import BpNetwork                                    # 贝叶斯BP神经网络
from Bayes_lstm import LstmRNN                                   # 贝叶斯LSTM神经网络
from Bayes_cnn import CNNNetwork                                 # 贝叶斯CNN神经网络
from scipy.stats import norm                                     # 正态分布统计函数
from Data_process_one import Savitzky                           # Savitzky-Golay滤波器
from Result_evaluate import DistanceUpandLow                    # 结果评估模块
from database_connect import time_series_data_insert, evaluation_data_insert, weather_type_data_insert  # 数据库连接模块

# 设置matplotlib中文字体和负号显示
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置OpenMP线程数为1，避免多线程冲突
os.environ["OMP_NUM_THREADS"] = '1'
def Two_level_output(stage_name="大兴",
                     path_root='F:/SanXia/Generate_PV_api/database',
                     granularity=1,
                     confidence=0.95,
                     day_by_day_date=['2025-01-01', '2025-01-02', '2025-01-03', '2025-01-04', 
                                     '2025-01-05', '2025-01-06', '2025-01-07'],
                     day_by_day_weather=['阴天', '多云', '晴天', '晴转多云', '多云转晴', '阴天', '晴天'],
                     hidden_size=[256, 256, 64],
                     batch_size=1,
                     monte_carlo_num=20,
                     database="arithmetic",
                     user="user_arithmetic",
                     password="dkdDfk@d3e9vPvd8f",
                     host="*************",
                     port="5866",
                     time_cycle=2,
                     project_id=652965,
                     project_name='ynfq',
                     user_id=1
                     ):
    """
    光伏出力二级输出预测函数
    
    基于用户指定天气类型的光伏功率预测和不确定性评估系统
    通过天气中心曲线和误差生成，结合贝叶斯神经网络进行光伏出力预测
    
    参数说明:
    :param stage_name: 电站名称
    :param path_root: 内置数据文件根目录
    :param granularity: 数据粒度，单位：分钟
    :param confidence: 置信度参数，实际置信度=2*confidence-1
    :param day_by_day_date: 逐日日期列表
    :param day_by_day_weather: 逐日天气类型列表
    :param hidden_size: 神经网络隐藏层神经元个数 [bp, cnn, lstm]
    :param batch_size: 训练批大小
    :param monte_carlo_num: 蒙特卡洛采样次数
    :param database: 生成结果存储数据库名称
    :param user: 数据库登录用户名
    :param password: 数据库登录密码
    :param host: 数据库所在服务器IP地址
    :param port: 数据库所在服务器连接端口
    :param time_cycle: 生成数据时间跨度(1:日, 2:周, 3:月, 4:年)
    :param project_id: 电站对应ID
    :param project_name: 电站名称对应简写符号
    :param user_id: 存储操作用户编号
    
    返回值:
    结果存储在 output result with two-level input 文件夹下
    """
    # ========== 基本参数设置 ==========
    z = norm.ppf(confidence)                     # 置信度对应的z值
    dim = int(1440 / granularity)               # 每日时间点数量
    weather_type = ['阴天', '多云', '晴天', '晴转多云', '多云转晴']  # 支持的天气类型
    path_root = os.path.join(path_root, stage_name)  # 电站数据根目录
    
    # 神经网络参数设置
    TIME = int(1440 / granularity)              # 每日时间点数量
    INPUT_SIZE = TIME                           # 输入层大小
    OUTPUT_SIZE = TIME                          # 输出层大小
    HIDDEN_LSTM = hidden_size[2]                # LSTM隐藏层大小
    HIDDEN_CNN = hidden_size[1]                 # CNN隐藏层大小
    HIDDEN_BP = hidden_size[0]                  # BP隐藏层大小
    BATCH_SIZE = batch_size                     # 批处理大小
    # ========== 结果存储路径设置 ==========
    path_to = os.path.join(path_root, f"output result with two-level input")
    if not os.path.exists(path_to):
        os.makedirs(path_to)
    
    # 输出文件路径定义
    PATH_SIM_RESULT = os.path.join(path_to, f"simulation_result_{granularity}min.xlsx")    # 仿真结果文件
    PATH_EVA_RESULT1 = os.path.join(path_to, f"evaluation_result_weather_type.xlsx")      # 天气类型评估结果
    PATH_EVA_RESULT2 = os.path.join(path_to, f"evaluation_result_indicator.xlsx")         # 指标评估结果
    # ========== 获取各种天气类型的气象中心曲线 ==========
    path_cluster = os.path.join(path_root, f"cluster data")
    path_cluster_result = os.path.join(path_cluster, f"cluster_result")
    
    # 各天气类型中心曲线文件路径
    path_center_y = os.path.join(path_cluster_result, f"center_阴天.csv")
    path_center_d = os.path.join(path_cluster_result, f"center_多云.csv")
    path_center_q = os.path.join(path_cluster_result, f"center_晴天.csv")
    path_center_qd = os.path.join(path_cluster_result, f"center_晴转多云.csv")
    path_center_dq = os.path.join(path_cluster_result, f"center_多云转晴.csv")
    
    # 读取并处理各天气类型的中心曲线
    center_y = pd.read_csv(path_center_y, header=None)
    center_y = np.reshape(center_y.values, (-1, dim))
    CENTER_Y = center_y[0]                      # 阴天中心曲线
    
    center_d = pd.read_csv(path_center_d, header=None)
    center_d = np.reshape(center_d.values, (-1, dim))
    CENTER_D = center_d[0]                      # 多云中心曲线
    
    center_q = pd.read_csv(path_center_q, header=None)
    center_q = np.reshape(center_q.values, (-1, dim))
    CENTER_Q = center_q[0]                      # 晴天中心曲线
    
    center_qd = pd.read_csv(path_center_qd, header=None)
    center_qd = np.reshape(center_qd.values, (-1, dim))
    CENTER_QD = center_y[0]                     # 晴转多云中心曲线（注：原代码中使用的是center_y[0]）
    
    center_dq = pd.read_csv(path_center_dq, header=None)
    center_dq = np.reshape(center_dq.values, (-1, dim))
    CENTER_DQ = center_y[0]                     # 多云转晴中心曲线（注：原代码中使用的是center_y[0]）

    # ========== 获取各天的天气曲线 ==========
    # 初始化误差和天气数组
    ERROR = np.zeros((len(day_by_day_weather), dim))        # 误差曲线数组
    WEATHER = np.zeros((len(day_by_day_weather), dim))      # 最终天气曲线数组
    path_error = os.path.join(path_root, f"weather error data")  # 天气误差数据路径
    
    # 对每一天生成天气曲线
    for day_index in range(len(day_by_day_weather)):
        # ========== 获得各天的误差曲线 ==========
        path_error_sub = os.path.join(path_error, day_by_day_weather[day_index])
        
        # 对每个时间点生成误差值
        for i in range(dim):
            if i == 0:
                ERROR[day_index, i] = 0  # 第一个时间点误差为0
            else:
                try:
                    # 尝试加载二维KDE模型（考虑时间相关性）
                    path_error_sub_two = os.path.join(path_error_sub, f"kde_model_{i}_(2).pkl")
                    with open(path_error_sub_two, 'rb') as f:
                        kde_i = pickle.load(f)
                    error_sample = kde_i.resample(100)
                    # 找到和前一时刻概率最接近的一组采样值
                    error_sample[:, 0] = abs(error_sample[:, 0] - ERROR[day_index, i-1])
                    # 该组采样值的后一时刻值就是当前时刻误差
                    ERROR[day_index, i] = error_sample[np.argmin(error_sample[:, 0]), 1]
                except:
                    try:
                        # 如果二维模型失败，尝试一维KDE模型
                        path_error_sub_one = os.path.join(path_error_sub, f"kde_model_{i}_(1).pkl")
                        with open(path_error_sub_one, 'rb') as f:
                            kde_i = pickle.load(f)
                        ERROR[day_index, i] = (kde_i.resample(1))
                    except:
                        # 如果都失败，误差设为0
                        ERROR[day_index, i] = 0
        
        # ========== 叠加中心曲线生成最终天气曲线 ==========
        if day_by_day_weather[day_index] == '阴天':
            WEATHER[day_index, :] = ERROR[day_index, :] + CENTER_Y
        elif day_by_day_weather[day_index] == '多云':
            WEATHER[day_index, :] = ERROR[day_index, :] + CENTER_D
        elif day_by_day_weather[day_index] == '晴天':
            WEATHER[day_index, :] = ERROR[day_index, :] + CENTER_Q
        elif day_by_day_weather[day_index] == '晴转多云':
            WEATHER[day_index, :] = ERROR[day_index, :] + CENTER_QD
        else:  # 多云转晴
            WEATHER[day_index, :] = ERROR[day_index, :] + CENTER_DQ
    # ========== 输入对应类型神经网络得到各天的生成结果 ==========
    # ========== 模型参数路径设置 ==========
    path_train = os.path.join(path_root, f"train data")
    path_train_y = os.path.join(path_train, f"阴天")           # 阴天模型路径
    path_train_d = os.path.join(path_train, f"多云")           # 多云模型路径
    path_train_q = os.path.join(path_train, f"晴天")           # 晴天模型路径
    path_train_dq = os.path.join(path_train, f"多云转晴")      # 多云转晴模型路径
    path_train_qd = os.path.join(path_train, f"晴转多云")      # 晴转多云模型路径
    
    # 各天气类型对应的模型参数文件路径
    PATH_Y_BP = os.path.join(path_train_y, f'bp_model_params.pth')
    PATH_Y_LSTM = os.path.join(path_train_y, f'lstm_model_params.pth')
    PATH_Y_CNN = os.path.join(path_train_y, f'cnn_model_params.pth')
    PATH_D_BP = os.path.join(path_train_d, f'bp_model_params.pth')
    PATH_D_LSTM = os.path.join(path_train_d, f'lstm_model_params.pth')
    PATH_D_CNN = os.path.join(path_train_d, f'cnn_model_params.pth')
    PATH_Q_BP = os.path.join(path_train_q, f'bp_model_params.pth')
    PATH_Q_LSTM = os.path.join(path_train_q, f'lstm_model_params.pth')
    PATH_Q_CNN = os.path.join(path_train_q, f'cnn_model_params.pth')
    PATH_DQ_BP = os.path.join(path_train_dq, f'bp_model_params.pth')
    PATH_DQ_LSTM = os.path.join(path_train_dq, f'lstm_model_params.pth')
    PATH_DQ_CNN = os.path.join(path_train_dq, f'cnn_model_params.pth')
    PATH_QD_BP = os.path.join(path_train_qd, f'bp_model_params.pth')
    PATH_QD_LSTM = os.path.join(path_train_qd, f'lstm_model_params.pth')
    PATH_QD_CNN = os.path.join(path_train_qd, f'cnn_model_params.pth')

    # ========== 初始化预测结果数组 ==========
    # 各模型的预测结果数组
    predictive_y_for_testing_lstm_mean = np.zeros((WEATHER.shape[0], WEATHER.shape[1]))
    predictive_y_for_testing_lstm_up = np.zeros((WEATHER.shape[0], WEATHER.shape[1]))
    predictive_y_for_testing_lstm_low = np.zeros((WEATHER.shape[0], WEATHER.shape[1]))
    predictive_y_for_testing_cnn_mean = np.zeros((WEATHER.shape[0], WEATHER.shape[1]))
    predictive_y_for_testing_cnn_up = np.zeros((WEATHER.shape[0], WEATHER.shape[1]))
    predictive_y_for_testing_cnn_low = np.zeros((WEATHER.shape[0], WEATHER.shape[1]))
    predictive_y_for_testing_bp_mean = np.zeros((WEATHER.shape[0], WEATHER.shape[1]))
    predictive_y_for_testing_bp_up = np.zeros((WEATHER.shape[0], WEATHER.shape[1]))
    predictive_y_for_testing_bp_low = np.zeros((WEATHER.shape[0], WEATHER.shape[1]))
    
    # 集成模型的最终预测结果
    predict_mean = np.zeros((WEATHER.shape[0], WEATHER.shape[1]))
    predict_up = np.zeros((WEATHER.shape[0], WEATHER.shape[1]))
    predict_low = np.zeros((WEATHER.shape[0], WEATHER.shape[1]))

    # ========== 对每一天进行预测 ==========
    for day in range(WEATHER.shape[0]):
        # 准备测试数据
        test_x = WEATHER[day, :].astype('float32').reshape(1, WEATHER.shape[1])
        
        # 根据天气类型选择对应的模型路径
        if day_by_day_weather[day] == '阴天':
            PATH1 = PATH_Y_BP
            PATH2 = PATH_Y_LSTM
            PATH3 = PATH_Y_CNN
        elif day_by_day_weather[day] == '多云':
            PATH1 = PATH_D_BP
            PATH2 = PATH_D_LSTM
            PATH3 = PATH_D_CNN
        elif day_by_day_weather[day] == '晴天':
            PATH1 = PATH_Q_BP
            PATH2 = PATH_Q_LSTM
            PATH3 = PATH_Q_CNN
        elif day_by_day_weather[day] == '多云转晴':
            PATH1 = PATH_DQ_BP
            PATH2 = PATH_DQ_LSTM
            PATH3 = PATH_DQ_CNN
        else:  # 晴转多云
            PATH1 = PATH_QD_BP
            PATH2 = PATH_QD_LSTM
            PATH3 = PATH_QD_CNN
        
        # 初始化蒙特卡洛采样结果数组
        std_error_mean_list = np.zeros((test_x.shape[0], test_x.shape[1], int(monte_carlo_num * 3)))
        # ========== LSTM模型预测 ==========
        predictive_y_for_testing_lstm = np.zeros((test_x.shape[0], test_x.shape[1], monte_carlo_num))
        for test_num in range(monte_carlo_num):
            # 加载LSTM模型并进行预测
            lstm_model = LstmRNN(n_input=INPUT_SIZE, n_hidden=HIDDEN_LSTM, n_output=OUTPUT_SIZE)
            lstm_model.load_state_dict(torch.load(PATH2))  # 从文件加载模型参数
            lstm_model = lstm_model.eval()  # 切换到测试模式
            test_x_tensor = test_x.reshape(-1, BATCH_SIZE, INPUT_SIZE)
            test_x_tensor = torch.from_numpy(test_x_tensor)
            predictive_y_for_testing_lstm[:, :, test_num] = lstm_model(test_x_tensor).view(-1, OUTPUT_SIZE).data.numpy()
            std_error_mean_list[:, :, test_num] = predictive_y_for_testing_lstm[:, :, test_num]
        
        # 计算LSTM预测结果的均值和置信区间
        predictive_y_for_testing_lstm_mean[day, :] = np.mean(predictive_y_for_testing_lstm, 2)
        std_error_lstm = np.std(predictive_y_for_testing_lstm, 2)
        predictive_y_for_testing_lstm_up[day, :] = predictive_y_for_testing_lstm_mean[day, :] + z * std_error_lstm
        predictive_y_for_testing_lstm_low[day, :] = predictive_y_for_testing_lstm_mean[day, :] - z * std_error_lstm

        # ========== CNN模型预测 ==========
        predictive_y_for_testing_cnn = np.zeros((test_x.shape[0], test_x.shape[1], monte_carlo_num))
        for test_num in range(monte_carlo_num):
            # 加载CNN模型并进行预测
            cnn_model = CNNNetwork(n_input=INPUT_SIZE, n_hidden=HIDDEN_CNN, n_output=OUTPUT_SIZE)
            cnn_model.load_state_dict(torch.load(PATH3))  # 从文件加载模型参数
            cnn_model = cnn_model.eval()  # 切换到测试模式
            predictive_y_for_testing_cnn[:, :, test_num] = cnn_model(test_x_tensor).view(-1, OUTPUT_SIZE).data.numpy()
            std_error_mean_list[:, :, monte_carlo_num + test_num] = predictive_y_for_testing_cnn[:, :, test_num]
        
        # 计算CNN预测结果的均值和置信区间
        predictive_y_for_testing_cnn_mean[day, :] = np.mean(predictive_y_for_testing_cnn, 2)
        std_error_res = np.std(predictive_y_for_testing_cnn, 2)
        predictive_y_for_testing_cnn_up[day, :] = predictive_y_for_testing_cnn_mean[day, :] + z * std_error_res
        predictive_y_for_testing_cnn_low[day, :] = predictive_y_for_testing_cnn_mean[day, :] - z * std_error_res


        # ========== BP神经网络模型预测 ==========
        predictive_y_for_testing_bp = np.zeros((test_x.shape[0], test_x.shape[1], monte_carlo_num))
        for test_num in range(monte_carlo_num):
            # 加载BP模型并进行预测
            bp_model = BpNetwork(n_input=INPUT_SIZE, n_hidden=HIDDEN_BP, n_output=OUTPUT_SIZE)
            bp_model.load_state_dict(torch.load(PATH1))  # 从文件加载模型参数
            bp_model = bp_model.eval()  # 切换到测试模式
            predictive_y_for_testing_bp[:, :, test_num] = bp_model(test_x_tensor).view(-1, OUTPUT_SIZE).data.numpy()
            std_error_mean_list[:, :, int(2 * monte_carlo_num + test_num)] = predictive_y_for_testing_bp[:, :, test_num]
        
        # 计算BP预测结果的均值和置信区间
        predictive_y_for_testing_bp_mean[day, :] = np.mean(predictive_y_for_testing_bp, 2)
        std_error_bp = np.std(predictive_y_for_testing_bp, 2)
        predictive_y_for_testing_bp_up[day, :] = predictive_y_for_testing_bp_mean[day, :] + z * std_error_bp
        predictive_y_for_testing_bp_low[day, :] = predictive_y_for_testing_bp_mean[day, :] - z * std_error_bp
        
        # ========== 集成模型结果计算 ==========
        # 计算三个模型的集成预测结果
        std_error_mean = np.std(std_error_mean_list, 2)
        predict_mean[day, :] = (predictive_y_for_testing_bp_mean[day, :] +
                               predictive_y_for_testing_cnn_mean[day, :] +
                               predictive_y_for_testing_lstm_mean[day, :]) / 3
        predict_up[day, :] = predict_mean[day, :] + z * std_error_mean
        predict_low[day, :] = predict_mean[day, :] - z * std_error_mean
    # ========== 后处理：滤波和非负性约束 ==========
    # 对预测结果进行Savitzky-Golay滤波，平滑处理
    for i in range(WEATHER.shape[0]):
        predict_mean[i] = Savitzky(predict_mean[i])
        predict_up[i] = Savitzky(predict_up[i])
        predict_low[i] = Savitzky(predict_low[i])
    
    # 确保预测结果非负（光伏出力不能为负）
    predict_low[predict_low < 0] = 0
    predict_up[predict_up < 0] = 0
    predict_mean[predict_mean < 0] = 0
    # ========== 天气类型数据存储 ==========
    # 将天气类型数据存储到数据库
    weather_type_data_insert(database=database,
                            user=user,
                            password=password,
                            host=host,
                            port=port,
                            time_cycle=time_cycle,
                            project_id=project_id,
                            project_name=project_name,
                            user_id=user_id,
                            time_flag=day_by_day_date,
                            weather_type=day_by_day_weather)
    
    # 将天气类型评估结果保存到Excel文件
    evaluation_weather_type = {'时间': day_by_day_date, '天气类型': day_by_day_weather}
    df = pd.DataFrame(evaluation_weather_type)
    df.to_excel(PATH_EVA_RESULT1, index=False)


    # ========== 评估指标计算和存储 ==========
    # 生成时间序列索引
    time_flag = pd.date_range(start=day_by_day_date[0], end=None, periods=TIME * len(day_by_day_date),
                             freq=f'{granularity}min', tz=None, normalize=False)
    
    # 计算上下界距离指标
    distance_upandlow_ave, distance_upandlow_max = DistanceUpandLow(predict_up, predict_low)
    
    # 将评估指标存储到数据库
    evaluation_data_insert(database=database,
                          user=user,
                          password=password,
                          host=host,
                          port=port,
                          time_cycle=time_cycle,
                          project_id=project_id,
                          project_name=project_name,
                          user_id=user_id,
                          time_flag=time_flag,
                          distance_upandlow_ave=distance_upandlow_ave,
                          distance_upandlow_max=distance_upandlow_max)
    
    # 将评估指标保存到Excel文件
    evaluation_indicator = {'上下界距离(ave,max)': [distance_upandlow_ave, distance_upandlow_max]}
    df = pd.DataFrame(evaluation_indicator)
    df.to_excel(PATH_EVA_RESULT2, index=False)    # ========== 仿真结果数据存储 ==========
    # 将多维数组展平为一维，便于存储
    predict_mean = predict_mean.flatten()
    predict_up = predict_up.flatten()
    predict_low = predict_low.flatten()
    
    # 将时间序列数据存储到数据库
    time_series_data_insert(database=database,
                           user=user,
                           password=password,
                           host=host,
                           port=port,
                           time_cycle=time_cycle,
                           project_id=project_id,
                           project_name=project_name,
                           user_id=user_id,
                           time_flag=time_flag,
                           predict_mean=predict_mean,
                           predict_up=predict_up,
                           predict_low=predict_low)
    
    # 将仿真结果保存到Excel文件
    simulation = {'时间': time_flag,
                 '生成出力': predict_mean,
                 '出力上界': predict_up,
                 '出力下界': predict_low}
    df = pd.DataFrame(simulation)
    df.to_excel(PATH_SIM_RESULT, index=False)


# ========== 主程序入口 ==========
if __name__ == "__main__":
    # 运行光伏出力二级输出预测函数
    Two_level_output()


    

    
 


