import torch.nn as nn
from blitz.modules import BayesianLinear
from Variational_estimator import variational_estimator

@variational_estimator
class BpNetwork(nn.Module):
    def __init__(self,n_input,n_hidden,n_output):
        super(BpNetwork,self).__init__()
        self.inputlayer = BayesianLinear(n_input,n_hidden) #此为输入层到隐藏层
        self.relu1=nn.ReLU()
        self.hiddenlayer = BayesianLinear(n_hidden,n_hidden) #此为隐藏层到输出层
        self.relu2=nn.ReLU()
        self.outputlayer = BayesianLinear(n_hidden,n_output) #此为隐藏层到输出层
        self.sigmoid = nn.Sigmoid()
    def forward(self,input):
        s, b, h = input.shape  # x is output, size (seq_len, batch, input_size)
        x = input.view(s*b, h) #变为二维张量
        x = self.inputlayer(x) #隐藏层加权
        x = self.relu1(x)
        x =self.hiddenlayer(x)
        x = self.relu2(x)
        x = self.outputlayer(x) #输出结果
        x = self.sigmoid(x)
        output = x.view(s, b, -1) #size (seq_len, batch, output_size)
        return output


