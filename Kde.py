from scipy.stats import gaussian_kde
import pandas as pd
import numpy as np
import pickle
import os


def Kernel_density_estimate(granularity=5,path_weather_cluster=None,path_weather_center=None,path_root=None):
    
    if path_weather_cluster == None:
        return print("请输入当前天气类型聚类结果路径")
    if path_weather_center == None:
        return print("请输入当前天气类型中心曲线路径")
    if path_root == None:
        return print("请输入当前天气类型误差分布结果存储路径")
    
    if not os.path.exists(path_root):
        os.makedirs(path_root)
    
    dim=int(1440/granularity) 
    weather_cluster=pd.read_csv(path_weather_cluster,header=None)
    weather_cluster=weather_cluster.values
    weather_cluster=np.reshape(weather_cluster,(-1,dim)) #类内气象曲线
    weather_center=pd.read_csv(path_weather_center,header=None)
    weather_center=weather_center.values
    weather_center=np.reshape(weather_center,(-1,dim))
    weather_center=weather_center[0,:] #气象中心曲线
    weather_error=np.zeros((weather_cluster.shape[0],dim))
    num_outliers=np.zeros(dim)
    for j in range (dim):
        for i in range (weather_error.shape[0]):
            weather_error[i,j]=weather_cluster[i,j]-weather_center[j] #每一列代表同一时刻不同气象曲线和气象中心曲线的误差
                                                                      #每一行代表某一条气象曲线不同时刻和气象中心曲线的误差
            if abs(weather_error[i,j])>1e-4:
                num_outliers[j]+=1


    #双变量核密度估计F(x=前一时刻的误差,y)
    for i in range(weather_error.shape[1]):
        if num_outliers[i]>(weather_error.shape[0]/3): 
            try:
                weather_error_i_current = weather_error[:,i] #当前时刻的误差
                weather_error_i_before = weather_error[:,i-1] #前一时刻的误差
                weather_error_i = np.vstack((weather_error_i_before,weather_error_i_current)) #第一列是前一个时刻的误差，第二列是当前时刻的误差
                kde_i=gaussian_kde(weather_error_i,bw_method='silverman')
                PATH=os.path.join(path_root, f'kde_model_{i}_(2).pkl')
            except:
                try :
                    weather_error_i = weather_error[:,i] #第一个时刻的误差
                    kde_i=gaussian_kde(weather_error_i,bw_method='silverman')
                    PATH=os.path.join(path_root, f'kde_model_{i}_(1).pkl')
                except:
                    weather_error_i[0]=weather_error_i[0]+1e-8
                    kde_i=gaussian_kde(weather_error_i,bw_method='silverman')
                    PATH=os.path.join(path_root, f'kde_model_{i}_(1).pkl')
            with open(PATH, 'wb') as f:
                pickle.dump(kde_i, f)
    
    
    

