#!/bin/bash

# 光伏出力预测API Docker运行脚本
# 提供Docker容器的构建、启动、停止等管理功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目配置
PROJECT_NAME="pv-prediction-api"
IMAGE_NAME="pv-prediction"
CONTAINER_NAME="pv-prediction-api"
PORT="8081"

# 显示使用说明
show_help() {
    echo -e "${BLUE}光伏出力预测API Docker管理脚本${NC}"
    echo ""
    echo "使用方法: $0 [命令]"
    echo ""
    echo "可用命令:"
    echo "  build      构建Docker镜像"
    echo "  start      启动服务"
    echo "  stop       停止服务"
    echo "  restart    重启服务"
    echo "  logs       查看日志"
    echo "  status     查看服务状态"
    echo "  clean      清理镜像和容器"
    echo "  health     检查服务健康状态"
    echo "  shell      进入容器Shell"
    echo "  help       显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 build    # 构建镜像"
    echo "  $0 start    # 启动服务"
    echo "  $0 logs     # 查看日志"
}

# 构建Docker镜像
build_image() {
    echo -e "${BLUE}正在构建Docker镜像...${NC}"
    docker build -t $IMAGE_NAME:latest .
    echo -e "${GREEN}镜像构建完成！${NC}"
}

# 启动服务
start_service() {
    echo -e "${BLUE}正在启动服务...${NC}"
    
    # 检查容器是否已存在
    if docker ps -a | grep -q $CONTAINER_NAME; then
        echo -e "${YELLOW}容器已存在，正在删除旧容器...${NC}"
        docker rm -f $CONTAINER_NAME
    fi
    
    # 启动新容器
    docker run -d \
        --name $CONTAINER_NAME \
        -p $PORT:$PORT \
        -v $(pwd)/database:/app/database \
        -v $(pwd)/configs:/app/configs \
        -v $(pwd)/logs:/app/logs \
        --restart unless-stopped \
        $IMAGE_NAME:latest
    
    echo -e "${GREEN}服务启动成功！${NC}"
    echo -e "${BLUE}访问地址: http://localhost:$PORT${NC}"
}

# 使用docker-compose启动
start_compose() {
    echo -e "${BLUE}使用docker-compose启动服务...${NC}"
    
    # 检查docker-compose.yml是否存在
    if [ ! -f "docker-compose.yml" ]; then
        echo -e "${RED}docker-compose.yml文件不存在！${NC}"
        exit 1
    fi
    
    docker-compose up -d
    echo -e "${GREEN}服务启动成功！${NC}"
    echo -e "${BLUE}访问地址: http://localhost:$PORT${NC}"
}

# 停止服务
stop_service() {
    echo -e "${BLUE}正在停止服务...${NC}"
    
    if docker ps | grep -q $CONTAINER_NAME; then
        docker stop $CONTAINER_NAME
        echo -e "${GREEN}服务停止成功！${NC}"
    else
        echo -e "${YELLOW}服务未运行${NC}"
    fi
}

# 停止docker-compose服务
stop_compose() {
    echo -e "${BLUE}使用docker-compose停止服务...${NC}"
    docker-compose down
    echo -e "${GREEN}服务停止成功！${NC}"
}

# 重启服务
restart_service() {
    echo -e "${BLUE}正在重启服务...${NC}"
    stop_service
    sleep 2
    start_service
}

# 查看日志
show_logs() {
    echo -e "${BLUE}显示服务日志...${NC}"
    if docker ps | grep -q $CONTAINER_NAME; then
        docker logs -f $CONTAINER_NAME
    else
        echo -e "${YELLOW}容器未运行${NC}"
    fi
}

# 查看状态
show_status() {
    echo -e "${BLUE}服务状态:${NC}"
    if docker ps | grep -q $CONTAINER_NAME; then
        echo -e "${GREEN}✓ 服务运行中${NC}"
        docker ps | grep $CONTAINER_NAME
    else
        echo -e "${RED}✗ 服务未运行${NC}"
    fi
    
    echo ""
    echo -e "${BLUE}镜像信息:${NC}"
    if docker images | grep -q $IMAGE_NAME; then
        docker images | grep $IMAGE_NAME
    else
        echo -e "${YELLOW}镜像未构建${NC}"
    fi
}

# 清理镜像和容器
clean_all() {
    echo -e "${BLUE}正在清理镜像和容器...${NC}"
    
    # 停止并删除容器
    if docker ps -a | grep -q $CONTAINER_NAME; then
        docker rm -f $CONTAINER_NAME
        echo -e "${GREEN}容器已删除${NC}"
    fi
    
    # 删除镜像
    if docker images | grep -q $IMAGE_NAME; then
        docker rmi $IMAGE_NAME:latest
        echo -e "${GREEN}镜像已删除${NC}"
    fi
    
    # 清理未使用的镜像
    docker image prune -f
    echo -e "${GREEN}清理完成！${NC}"
}

# 健康检查
health_check() {
    echo -e "${BLUE}正在检查服务健康状态...${NC}"
    
    if ! docker ps | grep -q $CONTAINER_NAME; then
        echo -e "${RED}✗ 服务未运行${NC}"
        exit 1
    fi
    
    # 尝试访问健康检查接口
    if curl -f http://localhost:$PORT/api/health >/dev/null 2>&1; then
        echo -e "${GREEN}✓ 服务健康${NC}"
    else
        echo -e "${RED}✗ 服务不健康${NC}"
        exit 1
    fi
}

# 进入容器Shell
enter_shell() {
    echo -e "${BLUE}进入容器Shell...${NC}"
    
    if docker ps | grep -q $CONTAINER_NAME; then
        docker exec -it $CONTAINER_NAME /bin/bash
    else
        echo -e "${RED}容器未运行${NC}"
        exit 1
    fi
}

# 主函数
main() {
    case "${1:-}" in
        build)
            build_image
            ;;
        start)
            build_image
            start_service
            ;;
        compose)
            start_compose
            ;;
        stop)
            stop_service
            ;;
        stop-compose)
            stop_compose
            ;;
        restart)
            restart_service
            ;;
        logs)
            show_logs
            ;;
        status)
            show_status
            ;;
        clean)
            clean_all
            ;;
        health)
            health_check
            ;;
        shell)
            enter_shell
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            echo -e "${RED}错误: 未知命令 '$1'${NC}"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"