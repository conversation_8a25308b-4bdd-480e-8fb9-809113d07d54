import torch
from torch import nn
from torch.nn import functional as F

class My_loss(nn.Module):
    def __init__(self):
        super().__init__()
        
      

    def forward(self, x, y):
        #47,1,288

        #自定义损失
        loss1=F.l1_loss(x,y,reduction='sum')

        loss2=F.mse_loss(x,y,reduction='sum')

        loss3=(torch.log(torch.cosh(y-x))).sum()

        loss4 = 0.5*loss1+0.5*loss2+0.5*loss3

        
        return loss4
    
    
  