# Docker部署指南

## 快速开始

### 1. 使用Docker运行脚本（推荐）

```bash
# 构建并启动服务
./docker-run.sh start

# 查看服务状态
./docker-run.sh status

# 查看日志
./docker-run.sh logs

# 停止服务
./docker-run.sh stop
```

### 2. 使用Docker Compose

```bash
# 启动服务（包含PostgreSQL数据库）
docker-compose up -d

# 停止服务
docker-compose down

# 查看日志
docker-compose logs -f
```

### 3. 手动Docker命令

```bash
# 构建镜像
docker build -t pv-prediction:latest .

# 启动容器
docker run -d \
  --name pv-prediction-api \
  -p 8081:8081 \
  -v $(pwd)/database:/app/database \
  -v $(pwd)/configs:/app/configs \
  -v $(pwd)/logs:/app/logs \
  pv-prediction:latest
```

## 服务验证

### 健康检查
```bash
curl -X GET http://localhost:8081/api/health
```

### 功能测试
```bash
# 一级输出预测
curl -X POST http://localhost:8081/api/prediction \
  -H "Content-Type: application/json" \
  -H "request-name: One level output" \
  -H "security-token: your_secure_token_here" \
  -d '{"station_name": "大兴", "start_time": "2024-01-01 00:00:00", "end_time": "2024-01-01 23:59:59"}'
```

## 文件结构

```
.
├── Dockerfile              # Docker镜像定义
├── docker-compose.yml      # Docker Compose配置
├── docker-run.sh          # Docker管理脚本
├── .dockerignore          # Docker忽略文件
├── configs/               # 配置文件目录
│   └── conf.json         # 主配置文件
├── database/             # 数据目录（挂载到容器）
└── logs/                 # 日志目录（挂载到容器）
```

## 配置说明

### 环境变量
- `PYTHONUNBUFFERED=1`: 禁用Python缓冲
- `NNPACK_DISABLE=1`: 禁用NNPACK警告
- `TORCH_CPP_LOG_LEVEL=ERROR`: 设置PyTorch日志级别

### 端口映射
- 容器端口：8081
- 主机端口：8081

### 数据持久化
- `./database:/app/database`: 数据文件挂载
- `./configs:/app/configs`: 配置文件挂载
- `./logs:/app/logs`: 日志文件挂载

## 故障排除

### 常见问题

1. **端口占用**
   ```bash
   # 查看端口占用
   lsof -i :8081
   # 修改docker-compose.yml中的端口映射
   ```

2. **权限问题**
   ```bash
   # 给脚本添加执行权限
   chmod +x docker-run.sh
   ```

3. **数据库连接问题**
   ```bash
   # 检查PostgreSQL服务状态
   docker-compose ps postgres
   # 查看数据库日志
   docker-compose logs postgres
   ```

### 日志查看
```bash
# 容器日志
docker logs pv-prediction-api

# 应用日志
tail -f logs/api_service.log

# 所有服务日志
docker-compose logs -f
```

## 性能优化

### 资源限制
在docker-compose.yml中添加：
```yaml
services:
  pv-api:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
```

### 生产环境建议
1. 使用外部数据库而非容器内数据库
2. 配置日志轮转
3. 设置健康检查和自动重启
4. 使用反向代理（如Nginx）
5. 启用HTTPS

## 备份和恢复

### 数据备份
```bash
# 备份数据目录
tar -czf backup-$(date +%Y%m%d).tar.gz database/

# 备份数据库
docker-compose exec postgres pg_dump -U pv_user pv_prediction > backup.sql
```

### 数据恢复
```bash
# 恢复数据目录
tar -xzf backup-20240101.tar.gz

# 恢复数据库
docker-compose exec postgres psql -U pv_user pv_prediction < backup.sql
```

## 更新升级

```bash
# 停止服务
./docker-run.sh stop

# 更新代码
git pull

# 重新构建并启动
./docker-run.sh start
```