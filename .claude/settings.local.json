{"permissions": {"allow": ["Bash(./venv/bin/pip show:*)", "Bash(./service_manager.sh:*)", "Bash(rg:*)", "Bash(grep:*)", "<PERSON><PERSON>(cat:*)", "Bash(find:*)", "Bash(./venv/bin/pip install --upgrade psycopg2-binary)", "Bash(./venv/bin/pip install psycopg)", "WebFetch(domain:highgo.com)", "Bash(./venv/bin/pip uninstall -y psycopg2-binary)", "Bash(./venv/bin/pip install:*)", "<PERSON><PERSON>(chmod:*)", "Bash(docker build:*)"], "deny": []}}