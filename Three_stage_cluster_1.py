from Cluster_first import cluster_first
from Cluster_second import cluster_second
from Cluster_third import cluster_third
import os
import shutil

def Three_stage_cluster(stage_name="大兴",
                        path_root="F:/SanXia/Generate_PV_api/database",
                        IP='*************',
                        DeviceID_weather='652965_ynfq-1646999496414',
                        DeviceID_power='652965_ynfq-1646999496407',
                        datatime='2024-04-14%2000:00:00,2025-04-13%2023:59:00',
                        power_label='jd2p',
                        weather_label=['afzd','zfzd','sfzd','hwd','sd','fs','fx','yl'],
                        capacity=40313.2,
                        granularity=1):
   
    """
    :param stage_name:电站名称
    :param path_root:内置数据文件根目录
    :param IP:api传入数据地址
    :param DeviceID_weather:环境设备ID
    :param DeviceID_power:功率设备ID
    :param datatime:训练历史数据时段
    :param power_label:功率标签
    :param weather_label:气象标签
    :param capacity:电站实际上网容量
    :param granularity:数据粒度,单位min
    :return 历史气象数据天气分类结果【晴天、阴天、多云、多云转晴、晴转多云】
    """

    path_to=os.path.join(os.path.join(path_root, stage_name),f'cluster data')
    path_root1=os.path.join(path_to, f'cluster_first')
    path_root2=os.path.join(path_to, f'cluster_second')
    path_root3=os.path.join(path_to, f'cluster_third')
    path_root_final=os.path.join(path_to, f'cluster_result')
    if not os.path.exists(path_root_final):
        os.makedirs(path_root_final)

    path_from_power_second=[os.path.join(path_root1, f'power_晴天等.csv')]
    path_from_weather_second=[os.path.join(path_root1, f'weather_晴天等.csv')]
    path_from_power_third=[os.path.join(path_root2, f'power_变化天气等.csv')]
    path_from_weather_third=[os.path.join(path_root2, f'weather_变化天气等.csv')]

    cluster_first(IP=IP,
                  DeviceID_weather=DeviceID_weather,
                  DeviceID_power=DeviceID_power,
                  datatime=datatime,
                  power_label=power_label,
                  weather_label=weather_label,
                  path_root=path_root1,
                  capacity=capacity,
                  granularity=granularity)
    shutil.copy(os.path.join(path_root1, f'center_阴天.csv'), path_root_final)
    shutil.copy(os.path.join(path_root1, f'power_阴天.csv'), path_root_final)
    shutil.copy(os.path.join(path_root1, f'weather_阴天.csv'), path_root_final)
    shutil.copy(os.path.join(path_root1, f'center_多云.csv'), path_root_final)
    shutil.copy(os.path.join(path_root1, f'power_多云.csv'), path_root_final)
    shutil.copy(os.path.join(path_root1, f'weather_多云.csv'), path_root_final)
    shutil.copy(os.path.join(path_root1, f'weather_statistics.csv'), path_root_final)
    
    cluster_second(path_from_power=path_from_power_second,
                  path_from_weather=path_from_weather_second,
                  path_root=path_root2,
                  granularity=granularity)
    shutil.copy(os.path.join(path_root2, f'center_晴天.csv'), path_root_final)
    shutil.copy(os.path.join(path_root2, f'power_晴天.csv'), path_root_final)
    shutil.copy(os.path.join(path_root2, f'weather_晴天.csv'), path_root_final)

    cluster_third(path_from_power=path_from_power_third,
                  path_from_weather=path_from_weather_third,
                  path_root=path_root3,
                  granularity=granularity)
    shutil.copy(os.path.join(path_root3, f'center_晴转多云.csv'), path_root_final)
    shutil.copy(os.path.join(path_root3, f'power_晴转多云.csv'), path_root_final)
    shutil.copy(os.path.join(path_root3, f'weather_晴转多云.csv'), path_root_final)
    shutil.copy(os.path.join(path_root3, f'center_多云转晴.csv'), path_root_final)
    shutil.copy(os.path.join(path_root3, f'power_多云转晴.csv'), path_root_final)
    shutil.copy(os.path.join(path_root3, f'weather_多云转晴.csv'), path_root_final)
    

if __name__=="__main__":
    Three_stage_cluster()