from Indicator import Indicator_third
import numpy as np
import random
import math


'''kmeans++,初始化质心'''
def GetClosestDist(point, centroids,middle_time):
    min_dist = math.inf  # 初始设为无穷大
    num_center=centroids.shape[0]

    for i in range(num_center):
        dist = Indicator_third(centroids[i,:], point,middle_time)
        if dist < min_dist:
            min_dist = dist
    return min_dist


def KppCenters(data_set,k,num_sample,dim,middle_time):

    data_copy=data_set.copy()
    np.random.shuffle(data_copy)
    cluster_centers = data_copy[:1,:]

    dist = [0 for _ in range(len(data_set))]
    for _ in range(1, k):
        total = 0.0
        for j in range(num_sample):
            dist[j] = GetClosestDist(data_set[j,:], cluster_centers,middle_time)  # 与最近一个聚类中心的距离
            total += dist[j]
        total *= random.random()
        for i, dist_i in enumerate(dist):  # 轮盘法选出下一个聚类中心；
            total -= dist_i
            if total > 0:
                continue
            cluster_centers=np.append(cluster_centers,data_set[i])
            cluster_centers=np.reshape(cluster_centers,(-1,dim))
            break

    cluster_centers = np.array(cluster_centers)
    cluster_centers=np.reshape(cluster_centers,(k,dim))

    return cluster_centers

'''聚类函数'''
def Cluster(u_matrix,num_sample):
    '''
    :param u_maxtrix:隶属度矩阵
    :param num_sample: 样本数量
    :return: 聚类结果
    '''
    cluster=[]
    for i in range(num_sample):
        list_i=u_matrix.T[i].tolist() #将隶属度矩阵的第i列转换为列表
        index_max=list_i.index(max(list_i)) #
        cluster.append(int(index_max))
    return cluster
 
 
'''计算隶属中心矩阵'''
def Center(data,u_matrix,weight,num_sample,num_cluster,dim):
    '''
    :param data:样本数据
    :param u_matrix: 隶属度矩阵
    :param weight: 模糊加权参数，一般取2
    :param num_sample: 样本数量
    :param num_cluster: 聚类数目
    :param dim: 数据维度
    :return: 隶属中心矩阵
    '''
    
    #进行初始聚类
    cluster=Cluster(u_matrix,num_sample)
    
    #初始化隶属中心矩阵
    center_matrix=np.zeros((num_cluster,dim))
 
    #计算隶属中心矩阵
      #初始化
    u_x=[np.zeros((1,dim)) for i in range(num_cluster)]
    u=[0 for i in range(num_cluster)]
      #计算
    for i in range(num_sample):
        category=cluster[i]  #样本类别
        u[category]=u[category]+(u_matrix[category][i])**weight
        u_x[category]=u_x[category]+((u_matrix[category][i])**weight)*data[i]
    
    for i in range(num_cluster):
        if(u[i]-0.0)==0:    #检查是否有0分母，若有则对其修正
            u[i]=1e-7
        center_matrix[i]=u_x[i]/u[i]
 
    #返回隶属中心矩阵
    return center_matrix
 
 
'''基于角距离计算各点到聚类中心的距离矩阵'''
def AngularDistance(data_power,data_weather,num_sample,center_matrix_power,center_matrix_weather,num_cluster,middle_time):
    '''
    :param data:样本数据
    :param num_sample: 样本数量
    :param center_matrix: 隶属中心矩阵
    :param num_cluster: 聚类数目
    :return: 基于DTW距离的距离矩阵
    '''
 
    '''初始化距离矩阵'''
    distance_matrix=np.zeros((num_cluster,num_sample))
    '''计算DTW距离'''
    for i in range(num_cluster):
        for j in range(num_sample):
            center_power=center_matrix_power[i]   #出力隶属中心
            sequence_power=data_power[j]  #出力数据点(序列)
            distance_matrix[i][j]=Indicator_third(center_power,sequence_power,middle_time)

    '''返回距离矩阵'''
    return distance_matrix
 
 
'''定义目标函数并返回目标函数值'''
def Function(num_sample,num_cluster,u_matrix,weight,distance_matrix):
    '''
    :param num_sample: 样本数量
    :param num_cluster: 聚类数目
    :param u_matrix: 隶属度矩阵
    :param weight: 模糊加权参数
    :param distance_matrix: 距离矩阵
    :return: 目标函数值
    '''
 
    '''初始化聚类'''
    cluster=Cluster(u_matrix,num_sample)
 
    '''计算目标函数值'''
    object=0    #目标函数值
    weight_list=[0 for i in range(num_cluster)]
    for i in range(num_sample):
        category=cluster[i]  #聚类类别
        d=distance_matrix[category][i]     #数据点到聚类中心的距离
        u=u_matrix[category][i]     #隶属度
        weight_list[category]=weight_list[category]+(u**weight)*(d**2)
    object=sum(weight_list)/(num_sample*24)
 
    '''返回目标函数值'''
    return object
 
 
'''更新隶属度矩阵'''
def UpdateU(num_sample,num_cluster,distance_matrix,weight):
    '''
    :param num_sample: 样本数量
    :param num_cluster: 聚类数目
    :param distance_matrix: 距离矩阵
    :param weight: 模糊加权参数
    :return: 更新后的隶属度矩阵
    '''
 
    '''初始化隶属度矩阵'''
    u_matrix=np.zeros((num_cluster,num_sample))
 
    '''更新隶属度矩阵'''
    for i in range(num_cluster):
        for j in range(num_sample):
           # r=0
            r=1e-10
            for k in range(num_cluster):
                if distance_matrix[k][j]==0:
                    distance_matrix[k][j]=1e-7
                r=r+(distance_matrix[i][j]/distance_matrix[k][j])**(2/(weight-1))
            u_matrix[i][j]=1/r
 
    '''返回更新后的隶属度矩阵'''
    return u_matrix
 
 
'''模糊C均值聚类函数'''
def KPPFCM(data_power,data_weather,num_cluster,iteration,weight,error,middle_time):
    '''
    :param data:样本数据
    :param num_cluster: 聚类数目
    :param iteration: 最大迭代步数
    :param weight: 模糊加权参数
    :param error: 迭代停止阈值，一般取0.001至0.01
    :return: 聚类结果
    '''
 
    '''样本数量'''
    num_sample=data_power.shape[0]
 
    '''数据维度'''
    dim=data_power.shape[1]
 
    '''初始化聚类中心'''
    center_matrix_power=KppCenters(data_power,num_cluster,num_sample,dim,middle_time)
    center_matrix_weather=KppCenters(data_weather,num_cluster,num_sample,dim,middle_time)

    '''存储目标函数值'''
    object=[]
 
    '''循环'''
    for i in range(iteration):
    
        #基于角距离计算各点到聚类中心的距离矩阵
        distance_matrix=AngularDistance(data_power,data_weather,num_sample,center_matrix_power,center_matrix_weather,num_cluster,middle_time) 
        #计算隶属度矩阵
        u_matrix=UpdateU(num_sample,num_cluster,distance_matrix,weight)
        #计算目标函数的值
        object.append(Function(num_sample,num_cluster,u_matrix,weight,distance_matrix))
        #更新聚类中心矩阵
        center_matrix_power=Center(data_power,u_matrix,weight,num_sample,num_cluster,dim)
        center_matrix_weather=Center(data_weather,u_matrix,weight,num_sample,num_cluster,dim)
        #判断阈值
        if i!=0 and abs(object[i-1]-object[i])<=error:
            break
 
    '''得到聚类结果'''
    cluster=Cluster(u_matrix,num_sample)
 
    '''返回聚类结果(聚类类别，聚类中心，目标函数值）'''
    return cluster,center_matrix_power,center_matrix_weather,object[-1]
 
 
