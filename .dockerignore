# Git相关文件
.git
.gitignore
.gitattributes

# Python相关文件
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 虚拟环境
venv/
env/
ENV/

# IDE相关文件
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
*.log
logs/
api_service.log
api_service.pid

# 临时文件
*.tmp
*.temp
.DS_Store
Thumbs.db

# Claude相关文件
.claude/
CLAUDE.md

# 文档文件
doc/
README.md
*.md

# 不必要的数据文件(保留重要的模型和配置)
*.xlsx
*.xls
*.csv
*.png
*.jpg
*.jpeg

# 但保留必要的配置文件
!configs/conf.json