from Bayes_bp_train import Baysian_bp_train
from Bayes_cnn_train import Baysian_cnn_train
from Bayes_lstm_train import Baysian_lstm_train
import os
def Model_train(stage_name="大兴",
                path_root="F:/SanXia/Generate_PV_api/database",
                weather_type_list=['阴天','多云','晴天','晴转多云','多云转晴'],
                granularity=1,
                max_epoch=[4500,4500,4500],
                hidden_size=[256,256,64],
                batch_size=1,
                epsilon=1e-4,
                lr=[1e-3,1e-3,1e-3],
                weight_decay=[0.005,0.005,0.005]):
    """
    :param stage_name:电站名称
    :param path_root:内置数据文件根目录
    :param weather_type_list:要训练的天气类型【一般为默认值,若某地历史数据缺失某种天气类型则按需改变】
    :param granularity:数据粒度,单位min
    :param max_epoch:最大训练次数[bp,cnn,lstm]
    :param hidden_size: 神经网络隐藏神经元个数[bp,cnn,lstm]
    :param batch_size: 训练批大小
    :param epsilon: 训练误差限度
    :param lr: 训练梯度下降学习率[bp,cnn,lstm]
    :param weight_decay: 训练权重衰减系数[bp,cnn,lstm]
    :return 给定天气类型下三种子神经网络的训练参数
    """
    time=int(1440/granularity)
    path_from=os.path.join(os.path.join(os.path.join(path_root, stage_name),f'cluster data'),f'cluster_result')
    path_to=os.path.join(os.path.join(path_root, stage_name),f'train data')
    path_power_list=[]
    path_weather_list=[]
    path_param_list=[]
    for i in range(len(weather_type_list)):
        path_power_list.append(os.path.join(path_from,f'power_{weather_type_list[i]}.csv'))
        path_weather_list.append(os.path.join(path_from,f'weather_{weather_type_list[i]}.csv'))
        path_param_list.append(os.path.join(path_to,f'{weather_type_list[i]}'))



    for i in range(len(path_power_list)):
        path_power = path_power_list[i]
        path_weather = path_weather_list[i]
        path_param = path_param_list[i]
        Baysian_bp_train(path_power,
                         path_weather,
                         path_param,
                         max_epoch[0],
                         time,
                         hidden_size[0],
                         batch_size,
                         epsilon,
                         lr[0],
                         weight_decay[0])
        Baysian_cnn_train(path_power,
                          path_weather,
                          path_param,
                          max_epoch[1],
                          time,
                          hidden_size[1],
                          batch_size,
                          epsilon,
                          lr[1],
                          weight_decay[1])
        Baysian_lstm_train(path_power,
                           path_weather,
                           path_param,
                           max_epoch[2],
                           time,
                           hidden_size[2],
                           batch_size,
                           epsilon,
                           lr[2],
                           weight_decay[2])

if __name__=="__main__":
    Model_train()