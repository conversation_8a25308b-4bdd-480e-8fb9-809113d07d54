import numpy as np
import matplotlib.pyplot as plt
from Data_process_two import PowerDataProcess,WeatherDataProcess
import pandas as pd
import math
from Kpp_fcm_second import KPPFCM
from Indicator import WeightedFluctuation
import os
plt.rcParams['font.sans-serif']=['SimHei']
plt.rcParams['axes.unicode_minus']=False
os.environ["OMP_NUM_THREADS"] = '1'

def cluster_second(path_from_power=["F:/SanXia/Generate_PV_api/database/大兴/cluster data/cluster_first/power_晴天等.csv"],
                  path_from_weather=["F:/SanXia/Generate_PV_api/database/大兴/cluster data/cluster_first/weather_晴天等.csv"],
                  path_root="F:/SanXia/Generate_PV_api/database/大兴/cluster data/cluster_second",
                  granularity=5):
    """
    :param path_from_power:历史功率文件列表
    :param path_from_weather:历史气象文件列表
    :param path_root:聚类结果存储根目录
    :param granularity:数据间隔，单位分钟
    :return 多云、变化天气 两类的日气象曲线、日出力曲线以及日出力中心曲线的csv文件
    """
    
    num_cluster=2
    iteration=100 #允许迭代上限次数
    weight=2 #权重系数
    error=1e-4 #允许误差
   
    if not os.path.exists(path_root):
        os.makedirs(path_root)


    '''数据处理：去噪、标准化、归一化'''
    data,dim,num_sample=PowerDataProcess(granularity,path_from_power)
    weather_factor,dim,num_sample=WeatherDataProcess(granularity,path_from_weather)
    
    '''判断聚类数量,并进行聚类,聚类指标是最大值和均值的欧氏距离,判断指标是CH'''
    cluster_final,center_matrix_power_final,center_matrix_weather_final,object_final=KPPFCM(data,weather_factor,num_cluster,iteration,weight,error)
    
    #print(cluster_final)
    '''保存聚类结果并输出'''
    power=[]
    weather=[]
    index=[0]
    num=0
    for k in range(num_cluster):
        for i in range(num_sample):
            if cluster_final[i]==k:
                num=num+1
                power.append(data[i]) 
                weather.append(weather_factor[i])
        index.append(num)

    power=np.array(power).reshape(-1,dim)
    weather=np.array(weather).reshape(-1,dim)
    
  
    '''计算类内指标均值，对该类的天气类型进行标记'''
    power_mean_max=[]
    mean=0
    for k in range(num_cluster):
        for i in range(index[k],index[k+1]):
            mean_s=0.8-np.mean(power[i,:])
            weighted_fluctuate_s=3.5*WeightedFluctuation(power[i,:])
            mean=mean+math.sqrt(mean_s**2+weighted_fluctuate_s**2)
        mean=mean/(index[k+1]-index[k])
        power_mean_max.append(mean)

    '''自动创建目录来保存不同类的结果'''
    cluster_class=['晴天','变化天气等']
    color_list=['lightgreen','gold','deepskyblue','orchid','lightcoral','mediumorchid']
    weather_statistics=[]
    for i in range(len(power_mean_max)):
        classi_index=np.argmin(power_mean_max) ##晴天是波动小的，变化天气是波动大的
        power_mean_max[classi_index]=1000
        path_power=os.path.join(path_root, f'power_{cluster_class[i]}.csv')
        path_weather=os.path.join(path_root, f'weather_{cluster_class[i]}.csv')
        path_center=os.path.join(path_root, f'center_{cluster_class[i]}.csv')
        power_temp=power[index[classi_index]:index[classi_index+1],:]
        weather_temp=weather[index[classi_index]:index[classi_index+1],:]
        df = pd.DataFrame(power_temp)
        df.to_csv(path_power,index=False,header=False)
        df = pd.DataFrame(weather_temp)
        df.to_csv(path_weather,index=False,header=False)
        cluster_center=np.vstack((np.reshape(center_matrix_weather_final[classi_index],(1,-1)),np.reshape(center_matrix_power_final[classi_index],(1,-1))))
        df = pd.DataFrame(cluster_center)
        df.to_csv(path_center,index=False,header=False)
        
    #    weather_statistics.append([f'class{i}',index[classi_index+1]-index[classi_index]])
    #    datas_draw = power_temp
    #    data_indicator=np.zeros((datas_draw.shape[0],2))
    #    for j in range(datas_draw.shape[0]):
    #        data_indicator[j,0]=0.8-np.mean(datas_draw[j,:])
    #        data_indicator[j,1]=3.5*WeightedFluctuation(datas_draw[j,:])
      
    #    plt.scatter(data_indicator[:,0],data_indicator[:,1],s=20,color=color_list[i])
    
    #plt.xlabel('0.8-mean')
    #plt.ylabel('WF')
    #plt.show()




    

                