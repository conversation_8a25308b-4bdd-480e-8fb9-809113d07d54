version: '3.8'

services:
  pv-api:
    image: generate_pv_api-pv-api:latest
    container_name: pv-prediction-api
    ports:
      - "8081:8081"
    volumes:
      - ./database:/app/database
      - ./configs:/app/configs
      - ./logs:/app/logs
    environment:
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
      - NNPACK_DISABLE=1
      - TORCH_CPP_LOG_LEVEL=ERROR
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - pv-network

networks:
  pv-network:
    driver: bridge