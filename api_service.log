2025-07-03 11:12:50 - INFO - ============================================================
2025-07-03 11:12:50 - INFO - 光伏出力预测API服务启动中...
2025-07-03 11:12:50 - INFO - ============================================================
2025-07-03 11:12:50 - INFO - 启动时间: 2025-07-03 11:12:50
2025-07-03 11:12:50 - INFO - 进程ID (PID): 52919
2025-07-03 11:12:50 - INFO - 配置文件: ./configs/conf.json
2025-07-03 11:12:50 - INFO - 监听端口: 8081
2025-07-03 11:12:50 - INFO - 安全认证: 启用
2025-07-03 11:12:50 - INFO - 工作目录: /Users/<USER>/workspace/长峡数能/1.项目列表/4.长电新能项目集/2.金下二期/研发过程文件/12集成架构/浙江大学算法/算法程序/20250603更新/Generate_PV_api/Generate_PV_api
2025-07-03 11:12:50 - INFO - ============================================================
2025-07-03 11:12:50 - INFO - 🚀 服务启动成功!
2025-07-03 11:12:50 - INFO - 📡 服务地址: http://localhost:8081
2025-07-03 11:12:50 - INFO - 📋 可用接口:
2025-07-03 11:12:50 - INFO -    - GET  http://localhost:8081/api/health
2025-07-03 11:12:50 - INFO -    - POST http://localhost:8081/api/output_service
2025-07-03 11:12:50 - INFO -    - POST http://localhost:8081/api/train_service
2025-07-03 11:12:50 - INFO - ============================================================
2025-07-03 11:12:50 - INFO - 💡 管理命令:
2025-07-03 11:12:50 - INFO -    查看进程: ps aux | grep 52919
2025-07-03 11:12:50 - INFO -    停止服务: kill 52919
2025-07-03 11:12:50 - INFO -    强制停止: kill -9 52919
2025-07-03 11:12:50 - INFO -    查看端口: lsof -i:8081
2025-07-03 11:12:50 - INFO - ============================================================
2025-07-03 11:12:50 - INFO - ⌨️  按 Ctrl+C 停止服务
2025-07-03 11:12:50 - INFO - 📈 服务运行中，等待请求...
2025-07-03 11:14:35 - INFO - 200 GET /api/health (127.0.0.1) 1.15ms
2025-07-03 11:15:13 - INFO - [One level output]
2025-07-03 11:17:23 - ERROR - [One level output] - error: 
                    瀚高数据库V4.5连接失败！
                    
                    可能的解决方案：
                    1. 联系瀚高数据库管理员修改pg_hba.conf，将认证方式改为md5：
                       host all all 0.0.0.0/0 md5
                    
                    2. 或者安装瀚高数据库专用的libpq库，替换当前的PostgreSQL客户端库
                    
                    3. 检查瀚高数据库服务器的认证配置，确保支持标准的PostgreSQL客户端连接
                    
                    原始错误: connection to server at "*************", port 5866 failed: authentication method 13 not supported

                    
2025-07-03 11:17:23 - INFO - [One level output] - output: {'success': False, 'reques name': 'One level output', 'result': '', 'errMsg': '\n                    瀚高数据库V4.5连接失败！\n                    \n                    可能的解决方案：\n                    1. 联系瀚高数据库管理员修改pg_hba.conf，将认证方式改为md5：\n                       host all all 0.0.0.0/0 md5\n                    \n                    2. 或者安装瀚高数据库专用的libpq库，替换当前的PostgreSQL客户端库\n                    \n                    3. 检查瀚高数据库服务器的认证配置，确保支持标准的PostgreSQL客户端连接\n                    \n                    原始错误: connection to server at "*************", port 5866 failed: authentication method 13 not supported\n\n                    '}
2025-07-03 11:17:23 - INFO - 200 POST /api/output_service (127.0.0.1) 130078.29ms
2025-07-03 16:58:35 - INFO - 200 GET /api/health (127.0.0.1) 3.43ms
2025-07-03 16:58:45 - INFO - [One level output]
2025-07-03 17:00:57 - ERROR - [One level output] - error: 
                    瀚高数据库V4.5连接失败！
                    
                    可能的解决方案：
                    1. 联系瀚高数据库管理员修改pg_hba.conf，将认证方式改为md5：
                       host all all 0.0.0.0/0 md5
                    
                    2. 或者安装瀚高数据库专用的libpq库，替换当前的PostgreSQL客户端库
                    
                    3. 检查瀚高数据库服务器的认证配置，确保支持标准的PostgreSQL客户端连接
                    
                    原始错误: connection to server at "*************", port 5866 failed: authentication method 13 not supported

                    
2025-07-03 17:00:57 - INFO - [One level output] - output: {'success': False, 'reques name': 'One level output', 'result': '', 'errMsg': '\n                    瀚高数据库V4.5连接失败！\n                    \n                    可能的解决方案：\n                    1. 联系瀚高数据库管理员修改pg_hba.conf，将认证方式改为md5：\n                       host all all 0.0.0.0/0 md5\n                    \n                    2. 或者安装瀚高数据库专用的libpq库，替换当前的PostgreSQL客户端库\n                    \n                    3. 检查瀚高数据库服务器的认证配置，确保支持标准的PostgreSQL客户端连接\n                    \n                    原始错误: connection to server at "*************", port 5866 failed: authentication method 13 not supported\n\n                    '}
2025-07-03 17:00:57 - INFO - 200 POST /api/output_service (127.0.0.1) 132501.31ms
2025-07-04 15:44:15 - INFO - 200 GET /api/health (127.0.0.1) 2.10ms
