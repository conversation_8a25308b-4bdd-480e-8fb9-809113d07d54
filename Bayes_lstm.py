from torch import nn
from blitz.modules import BayesianLSTM
from Variational_estimator import variational_estimator

'''定义LSTM网络'''
@variational_estimator
class LstmRNN(nn.Module):
    '''
    :param input_size: 输入层神经元个数
    :param hidden_size: 隐藏层神经元个数
    :param output_size: 输出层神经元个数
    :param num_layers: LSTM神经网络层数
    '''
    def __init__(self,n_input,n_hidden,n_output):
        super().__init__()
        self.lstm1 = BayesianLSTM(n_input, n_hidden)  
        self.ForwardCalculation = nn.Linear(n_hidden, n_output)
        #self.sigmoid = nn.Sigmoid()
    
    '''前向传播函数'''
    def forward(self, x):
        x, _ = self.lstm1(x)  # _x is input, size (seq_len, batch, input_size)
        s, b, h = x.shape  # x is output, size (seq_len, batch, hidden_size)
        x = x.view(s*b, h) #变为二维张量
        x = self.ForwardCalculation(x) #得到最终输出
        #x= self.sigmoid(x)
        x = x.view(s, b, -1)
        return x


