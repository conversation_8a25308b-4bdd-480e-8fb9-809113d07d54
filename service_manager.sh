#!/bin/bash

# 光伏出力预测API服务管理脚本
# 使用方法: ./service_manager.sh [start|stop|restart|status|logs]

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PID_FILE="$SCRIPT_DIR/api_service.pid"
LOG_FILE="$SCRIPT_DIR/api_service.log"
PYTHON_CMD="$SCRIPT_DIR/venv/bin/python"
API_SCRIPT="$SCRIPT_DIR/api_producter.py"
SERVICE_NAME="光伏出力预测API服务"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务是否运行
check_service() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p $PID > /dev/null 2>&1; then
            return 0  # 服务正在运行
        else
            rm -f "$PID_FILE"  # 清理无效的PID文件
            return 1  # 服务未运行
        fi
    else
        return 1  # 服务未运行
    fi
}

# 启动服务
start_service() {
    print_info "启动 $SERVICE_NAME..."
    
    if check_service; then
        PID=$(cat "$PID_FILE")
        print_warning "服务已在运行 (PID: $PID)"
        return 1
    fi
    
    # 检查Python环境
    if [ ! -f "$PYTHON_CMD" ]; then
        print_error "Python虚拟环境不存在: $PYTHON_CMD"
        print_info "请先创建虚拟环境或检查路径"
        return 1
    fi
    
    # 检查API脚本
    if [ ! -f "$API_SCRIPT" ]; then
        print_error "API脚本不存在: $API_SCRIPT"
        return 1
    fi
    
    # 启动服务
    cd "$SCRIPT_DIR"
    # 设置环境变量以抑制 NNPACK 和其他 PyTorch 警告
    export NNPACK_DISABLE=1
    export TORCH_CPP_LOG_LEVEL=ERROR
    nohup "$PYTHON_CMD" "$API_SCRIPT" > "$LOG_FILE" 2>&1 &
    PID=$!
    echo $PID > "$PID_FILE"
    
    # 等待服务启动
    sleep 3
    
    if check_service; then
        print_success "服务启动成功 (PID: $PID)"
        print_info "日志文件: $LOG_FILE"
        print_info "PID文件: $PID_FILE"
        
        # 检查服务健康状态
        if command -v curl > /dev/null 2>&1; then
            print_info "检查服务健康状态..."
            sleep 2
            RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8081/api/health 2>/dev/null)
            if [ "$RESPONSE" = "200" ]; then
                print_success "服务健康检查通过 ✓"
            else
                print_warning "服务健康检查失败，请查看日志"
            fi
        fi
    else
        print_error "服务启动失败，请查看日志: $LOG_FILE"
        rm -f "$PID_FILE"
        return 1
    fi
}

# 停止服务
stop_service() {
    print_info "停止 $SERVICE_NAME..."
    
    if ! check_service; then
        print_warning "服务未运行"
        return 1
    fi
    
    PID=$(cat "$PID_FILE")
    print_info "正在停止进程 (PID: $PID)..."
    
    # 优雅停止
    kill $PID 2>/dev/null
    
    # 等待进程结束
    for i in {1..10}; do
        if ! ps -p $PID > /dev/null 2>&1; then
            break
        fi
        sleep 1
    done
    
    # 如果进程仍在运行，强制杀死
    if ps -p $PID > /dev/null 2>&1; then
        print_warning "优雅停止失败，强制终止进程..."
        kill -9 $PID 2>/dev/null
        sleep 1
    fi
    
    # 清理PID文件
    rm -f "$PID_FILE"
    print_success "服务已停止"
}

# 重启服务
restart_service() {
    print_info "重启 $SERVICE_NAME..."
    stop_service
    sleep 2
    start_service
}

# 查看服务状态
status_service() {
    echo "=================================="
    echo "      $SERVICE_NAME 状态"
    echo "=================================="
    
    if check_service; then
        PID=$(cat "$PID_FILE")
        print_success "服务状态: 运行中"
        echo "进程ID: $PID"
        echo "启动时间: $(ps -p $PID -o lstart= 2>/dev/null | awk '{print $2" "$3" "$4" "$5" "$6}' | xargs -I {} date -j -f "%b %d %H:%M:%S %Y" "{}" "+%Y-%m-%d %H:%M:%S" 2>/dev/null || echo '未知')"    
        echo "内存使用: $(ps -p $PID -o rss= 2>/dev/null | awk '{print $1/1024 " MB"}' || echo '未知')"
        echo "CPU使用: $(ps -p $PID -o %cpu= 2>/dev/null || echo '未知')%"
        
        # 检查端口监听
        if command -v lsof > /dev/null 2>&1; then
            PORT_INFO=$(lsof -i:8081 -P 2>/dev/null | grep LISTEN)
            if [ -n "$PORT_INFO" ]; then
                echo "监听端口: 8081"
            else
                print_warning "未检测到端口监听"
            fi
        fi
        
        # 检查服务健康状态
        if command -v curl > /dev/null 2>&1; then
            RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8081/api/health 2>/dev/null)
            if [ "$RESPONSE" = "200" ]; then
                print_success "健康状态: 正常 ✓"
            else
                print_warning "健康状态: 异常 ✗"
            fi
        fi
    else
        print_error "服务状态: 未运行"
    fi
    
    echo "=================================="
    echo "日志文件: $LOG_FILE"
    echo "PID文件: $PID_FILE"
    echo "=================================="
}

# 查看日志
show_logs() {
    if [ -f "$LOG_FILE" ]; then
        print_info "显示服务日志 (最后50行):"
        echo "=================================="
        tail -n 50 "$LOG_FILE"
        echo "=================================="
        print_info "使用 'tail -f $LOG_FILE' 实时查看日志"
    else
        print_warning "日志文件不存在: $LOG_FILE"
    fi
}

# 主逻辑
case "$1" in
    start)
        start_service
        ;;
    stop)
        stop_service
        ;;
    restart)
        restart_service
        ;;
    status)
        status_service
        ;;
    logs)
        show_logs
        ;;
    *)
        echo "使用方法: $0 {start|stop|restart|status|logs}"
        echo ""
        echo "命令说明:"
        echo "  start   - 启动服务"
        echo "  stop    - 停止服务"
        echo "  restart - 重启服务"
        echo "  status  - 查看服务状态"
        echo "  logs    - 查看服务日志"
        echo ""
        echo "示例:"
        echo "  $0 start    # 启动服务"
        echo "  $0 status   # 查看状态"
        echo "  $0 logs     # 查看日志"
        exit 1
        ;;
esac

exit 0
