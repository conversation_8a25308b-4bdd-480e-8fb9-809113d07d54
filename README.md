# 出力生成算法程序

## 启动方式

### 1. 环境准备

#### 安装Python依赖包
```
pip install tornado pyyaml nest-asyncio requests urllib3 "numpy<2" pandas torch scipy scikit-learn matplotlib seaborn psycopg2-binary blitz-bayesian-pytorch detecta

安装日志
Successfully installed blitz-bayesian-pytorch-0.2.8 kiwisolver-1.3.1 matplotlib-3.3.4 pillow-8.4.0 psycopg2-binary-2.9.8 pyparsing-3.1.4 seaborn-0.11.2 torchvision-0.11.2
```



**注意**: 
- 必须安装 `numpy<2` 版本以避免与PyTorch的兼容性问题
- 需要安装 `blitz-bayesian-pytorch` 来支持贝叶斯神经网络

#### 配置文件设置
服务使用 `configs/conf.json` 配置文件，默认配置如下：
```json
{
  "security": "your_secure_token_here",
  "interception": true,
  "port": 8081
}
```

- `security`: API调用时的安全令牌
- `interception`: 是否启用token认证拦截
- `port`: 服务运行端口

### 2. 启动服务

#### 方法一：使用管理脚本启动（推荐）
```bash
# 启动服务
./service_manager.sh start

# 查看服务状态
./service_manager.sh status

# 查看服务日志
./service_manager.sh logs

# 停止服务
./service_manager.sh stop

# 重启服务
./service_manager.sh restart
```

#### 方法二：直接启动
```bash
python api_producter.py
```

#### 方法三：指定配置文件启动
```bash
python api_producter.py --config_file ./configs/conf.json
```

#### 方法四：使用虚拟环境启动
```bash
# 如果使用虚拟环境
./venv/bin/python api_producter.py
```

### 3. 服务管理命令

#### 查看服务状态
```bash
# 使用管理脚本（推荐）
./service_manager.sh status

# 手动查看进程
ps aux | grep api_producter.py

# 查看端口占用
lsof -i:8081
```

#### 停止服务
```bash
# 使用管理脚本（推荐）
./service_manager.sh stop

# 手动停止（需要先获取PID）
kill <PID>

# 强制停止
kill -9 <PID>

# 或者按进程名停止
pkill -f api_producter.py
```

#### 查看日志
```bash
# 使用管理脚本查看日志
./service_manager.sh logs

# 实时查看日志
tail -f api_service.log

# 如果直接启动，查看控制台输出
```

### 3. 验证服务状态
服务启动后，可以通过健康检查接口验证：
```bash
curl -X GET http://localhost:8081/api/health
```
正常情况下应返回：`OK!`

### 4. 常见问题排查

#### 端口被占用
如果遇到 `Address already in use` 错误，请：
1. 修改 `configs/conf.json` 中的端口号
2. 或者停止占用端口的进程：`lsof -ti:8081 | xargs kill -9`

#### 依赖包问题
如果遇到模块导入错误：
1. 确保安装了所有必需的依赖包
2. 特别注意numpy版本要求：`pip install "numpy<2"`
3. 安装贝叶斯网络支持：`pip install blitz-bayesian-pytorch`

#### 数据库连接问题
如果遇到数据库连接错误，请检查：
1. 数据库服务器是否运行
2. 连接参数是否正确（host、port、user、password）
3. 数据库认证方法是否支持

## 程序结构说明

### 模型训练模块（半年执行一次）
- `Three_stage_cluster_1.py` - 三阶段天气聚类
- `Model_train_2.py` - 神经网络模型训练
- `Weather_error_generate_3.py` - 天气误差分布估计

### 预测服务模块（API后台逻辑）
- `One_level_output_4.py` - 一级输出：基于历史数据的自动天气类型判断预测
- `Two_level_output_5.py` - 二级输出：基于用户指定天气类型的预测

### API接口入口
- `api_producter.py` - 主API服务入口文件

## API接口说明

服务提供以下接口：

1. **健康检查接口**: `GET /api/health`
   - 返回服务状态

2. **光伏出力预测服务**: `POST /api/output_service`
   - 支持两种预测模式：
     - "One level output": 一级输出（自动天气类型判断）
     - "Two level output": 二级输出（指定天气类型）

3. **模型训练服务**: `POST /api/train_service`
   - 支持三种训练功能：
     - "Weather cluster": 天气聚类
     - "NN param train": 神经网络参数训练
     - "Weather error distribution estimate": 天气误差分布估计

### 请求头要求
所有POST请求都需要包含以下请求头：
- `Content-Type: application/json`
- `request-name: [具体的请求类型]`
- `security-token: [配置文件中的安全令牌]`


## API调用示例

### 示例1：健康检查
验证API服务是否正常运行：

```bash
curl -X GET http://localhost:8081/api/health
```

### 示例2：一级输出预测（自动天气类型判断）
基于历史气象数据自动判断天气类型并进行光伏出力预测：

```bash
curl -X POST http://localhost:8081/api/output_service \
  -H "Content-Type: application/json" \
  -H "request-name: One level output" \
  -H "security-token: your_secure_token_here" \
  -d '{
    "IP": "*************",
    "DeviceID_weather": "652965_ynfq-1646999496414",
    "DeviceID_power": "652965_ynfq-1646999496407",
    "datatime": "2025-03-14%2000:00:00,2025-04-13%2023:59:00",
    "stage_name": "大兴",
    "capacity": 40313.2,
    "path_root": "./database",
    "granularity": 1,
    "confidence": 0.95,
    "power_label": "jd2p",
    "weather_label": ["afzd", "zfzd", "sfzd", "hwd", "sd", "fs", "fx", "yl"],
    "time_label": "timestamp",
    "hidden_size": [256, 256, 64],
    "batch_size": 1,
    "monte_carlo_num": 20,
    "database": "arithmetic",
    "user": "user_arithmetic",
    "password": "dkdDfk@d3e9vPvd8f",
    "host": "*************",
    "port": "5866",
    "time_cycle": 3,
    "project_id": 652965,
    "project_name": "ynfq",
    "user_id": 1
  }'
```

### 示例3：二级输出预测（指定天气类型）
用户指定每天的天气类型进行光伏出力预测：

```bash
curl -X POST http://localhost:8081/api/output_service \
  -H "Content-Type: application/json" \
  -H "request-name: Two level output" \
  -H "security-token: your_secure_token_here" \
  -d '{
    "stage_name": "大兴",
    "path_root": "./database",
    "granularity": 1,
    "confidence": 0.95,
    "day_by_day_date": ["2025-01-01", "2025-01-02", "2025-01-03", "2025-01-04", "2025-01-05", "2025-01-06", "2025-01-07"],
    "day_by_day_weather": ["阴天", "多云", "晴天", "晴转多云", "多云转晴", "阴天", "晴天"],
    "hidden_size": [256, 256, 64],
    "batch_size": 1,
    "monte_carlo_num": 20,
    "database": "arithmetic",
    "user": "user_arithmetic",
    "password": "dkdDfk@d3e9vPvd8f",
    "host": "*************",
    "port": "5866",
    "time_cycle": 2,
    "project_id": 652965,
    "project_name": "ynfq",
    "user_id": 1
  }'
```

### 参数说明

#### 一级输出预测主要参数：
- `IP`: 历史数据存储库服务器IP地址
- `DeviceID_weather`: 气象数据设备ID
- `DeviceID_power`: 功率数据设备ID
- `datatime`: 预测时间范围（URL编码格式）
- `stage_name`: 电站名称
- `capacity`: 电站额定容量（kW）
- `granularity`: 数据粒度（分钟）
- `confidence`: 置信度参数（实际置信度=2*confidence-1）

#### 二级输出预测主要参数：
- `day_by_day_date`: 逐日日期列表
- `day_by_day_weather`: 对应的逐日天气类型
- 支持的天气类型：["阴天", "多云", "晴天", "晴转多云", "多云转晴"]
