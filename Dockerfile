# 使用Python 3.11官方镜像作为基础镜像
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV NNPACK_DISABLE=1
ENV TORCH_CPP_LOG_LEVEL=ERROR

# 更新包管理器并安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 首先复制requirements.txt（如果存在）
COPY requirements.txt* /app/

# 安装Python依赖 - 分步安装避免网络问题
RUN pip install --no-cache-dir --upgrade pip

# 安装基础依赖
RUN pip install --no-cache-dir tornado pyyaml nest-asyncio requests urllib3

# 安装数据科学基础包
RUN pip install --no-cache-dir "numpy<2" pandas

# 安装科学计算包
RUN pip install --no-cache-dir scipy scikit-learn

# 安装可视化包
RUN pip install --no-cache-dir matplotlib seaborn

# 安装数据库连接包
RUN pip install --no-cache-dir psycopg2-binary

# 安装PyTorch (CPU版本)
RUN pip install --no-cache-dir torch --index-url https://download.pytorch.org/whl/cpu

# 安装贝叶斯神经网络包
RUN pip install --no-cache-dir blitz-bayesian-pytorch

# 安装峰值检测包
RUN pip install --no-cache-dir detecta

# 复制项目文件
COPY . /app/

# 创建必要的目录
RUN mkdir -p /app/logs

# 暴露端口
EXPOSE 8081

# 设置健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8081/api/health || exit 1

# 启动命令
CMD ["python", "api_producter.py"]