-- xxx数据表
create table public.tgeem_oe_power_evaluation (
  id bigint primary key not null,
  time_cycle smallint,
  project_id character varying(64) not null,
  project_name character varying(100),
  granularity_time_begin timestamp without time zone,
  granularity_time_end timestamp without time zone,
  cover_rate numeric(10,4),
  distance_upandlow_ave numeric(10,4),
  distance_upandlow_max numeric(10,4),
  pinball_ave numeric(10,4),
  pinball_max numeric(10,4),
  daily_max_power_error_ave numeric(10,4),
  daily_max_power_error_max numeric(10,4),
  daily_ave_power_error_ave numeric(10,4),
  daily_ave_power_error_max numeric(10,4),
  eighty_percent_max_power_generation_time_error numeric(10,4),
  create_user bigint,
  create_time timestamp without time zone,
  update_user bigint,
  update_time timestamp without time zone,
  status integer default 1,
  is_deleted integer default 0
);

-- 光伏出力预测数据表
create table public.tgeem_oe_power_prediction (
  id bigint primary key not null, -- 主键ID，自动生成
  time_cycle smallint, -- 时间维度（1:日, 2:周, 3:月, 4:年）
  project_id character varying(64) not null, -- 场站ID
  project_name character varying(100), -- 场站名称
  granularity_time timestamp without time zone, -- 时间点（格式：yyyy-MM-dd HH:mm:ss）
  generating_output numeric(10,2), -- 生成出力（预测值）（单位：kW）
  output_upper_bound numeric(10,2), -- 出力上界（单位：kW）
  output_lower_bound numeric(10,2), -- 出力下界（单位：kW）
  actual_output numeric(10,2), -- 实际出力（实际值）（单位：kW）
  create_user bigint, -- 创建用户
  create_time timestamp without time zone, -- 创建时间
  update_user bigint, -- 更新用户
  update_time timestamp without time zone, -- 更新时间
  status integer default 1, -- 状态（1.有效）
  is_deleted integer default 0 -- 逻辑删除（0.未删除，1.已删除）
);
create index idx_project_cycle_time on tgeem_oe_power_prediction using btree (project_id, time_cycle, granularity_time);
comment on table public.tgeem_oe_power_prediction is '光伏出力预测数据表';
comment on column public.tgeem_oe_power_prediction.id is '主键ID，自动生成';
comment on column public.tgeem_oe_power_prediction.time_cycle is '时间维度（1:日, 2:周, 3:月, 4:年）';
comment on column public.tgeem_oe_power_prediction.project_id is '场站ID';
comment on column public.tgeem_oe_power_prediction.project_name is '场站名称';
comment on column public.tgeem_oe_power_prediction.granularity_time is '时间点（格式：yyyy-MM-dd HH:mm:ss）';
comment on column public.tgeem_oe_power_prediction.generating_output is '生成出力（预测值）（单位：kW）';
comment on column public.tgeem_oe_power_prediction.output_upper_bound is '出力上界（单位：kW）';
comment on column public.tgeem_oe_power_prediction.output_lower_bound is '出力下界（单位：kW）';
comment on column public.tgeem_oe_power_prediction.actual_output is '实际出力（实际值）（单位：kW）';
comment on column public.tgeem_oe_power_prediction.create_user is '创建用户';
comment on column public.tgeem_oe_power_prediction.create_time is '创建时间';
comment on column public.tgeem_oe_power_prediction.update_user is '更新用户';
comment on column public.tgeem_oe_power_prediction.update_time is '更新时间';
comment on column public.tgeem_oe_power_prediction.status is '状态（1.有效）';
comment on column public.tgeem_oe_power_prediction.is_deleted is '逻辑删除（0.未删除，1.已删除）';

-- xxx数据表
create table public.tgeem_oe_weather_type (
  id bigint primary key not null,
  time_cycle smallint,
  project_id character varying(64) not null,
  project_name character varying(100),
  granularity_time timestamp without time zone,
  weather_type character varying(50),
  create_user bigint,
  create_time timestamp without time zone,
  update_user bigint,
  update_time timestamp without time zone,
  status integer default 1,
  is_deleted integer default 0
);
