from Kde import Kernel_density_estimate
import os

def Weather_error_generate(stage_name="大兴",
                           path_root='F:/SanXia/Generate_PV_api/database',
                           weather_type=['阴天','多云','晴天','晴转多云','多云转晴'],
                           granularity=1):

    """
    :param stage_name:电站名称
    :param path_root:内置数据文件根目录
    :param weather_type_list:要训练的天气类型【一般为默认值,若某地历史数据缺失某种天气类型则按需改变】
    :param granularity:数据粒度,单位min
    :return 给定天气类型下的气象曲线误差分布模型
    """
    path_root=os.path.join(path_root,stage_name)
    path_cluster=os.path.join(path_root,f"cluster data")
    path_cluster_result=os.path.join(path_cluster,f"cluster_result")
    path_to=os.path.join(path_root,f"weather error data")
    
    for i in range(len(weather_type)):
        path_weather=os.path.join(path_cluster_result,f"weather_{weather_type[i]}.csv")
        path_weather_center=os.path.join(path_cluster_result,f"center_{weather_type[i]}.csv")
        path_to_sub=os.path.join(path_to,weather_type[i])
        Kernel_density_estimate(granularity,path_weather,path_weather_center,path_to_sub)

    
if __name__=="__main__":
    Weather_error_generate()