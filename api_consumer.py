import requests
import hashlib
import time
from datetime import datetime, timedelta
import bisect
import urllib3  
import calendar

# 禁用全局 SSL 验证警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def get_last_day(year, month):
    return calendar.monthrange(year, month)[1]

def split_time_intervals(input_str):
    # 解析输入字符串
    start_str, end_str = input_str.split(',')
    start_str = start_str.replace('%20', ' ')
    end_str = end_str.replace('%20', ' ')
    start_time = datetime.strptime(start_str, '%Y-%m-%d %H:%M:%S')
    end_time = datetime.strptime(end_str, '%Y-%m-%d %H:%M:%S')
    
    start_days = [1, 6, 11, 16, 21, 26]
    intervals = []
    current = start_time
    
    while current < end_time:
        year = current.year
        month = current.month
        current_day = current.day
        
        # 计算当月最后一天
        last_day = get_last_day(year, month)
        
        # 确定当前所在的分割区间
        idx = bisect.bisect_right(start_days, current_day) - 1
        if idx < 0:
            idx = 0  # 处理current_day小于1的情况（理论上不会出现）
        
        start_day = start_days[idx]
        
        # 计算区间结束日
        if idx < len(start_days) - 1:
            end_day_candidate = start_day + 4
            end_day = min(end_day_candidate, last_day)
        else:
            end_day = last_day
        
        # 生成理论时间区间
        theory_start = datetime(year, month, start_day, 0, 0, 0)
        theory_end = datetime(year, month, end_day, 23, 59, 59)
        
        # 确定实际区间
        interval_start = max(current, theory_start)
        interval_end = min(theory_end, end_time)
        
        if interval_start <= interval_end:
            intervals.append((interval_start, interval_end))
        
        # 推进到下一个区间的起始点
        current = interval_end + timedelta(seconds=1)
    
    # 格式化输出
    result = []
    for start, end in intervals:
        start_str = start.strftime('%Y-%m-%d %H:%M:%S').replace(' ', '%20')
        end_str = end.strftime('%Y-%m-%d %H:%M:%S').replace(' ', '%20')
        result.append(f"{start_str},{end_str}")
    
    return result


def data_post(IP='*************',DeviceID='652965_ynfq-1646999496414',datatime='2025-04-15%2009:50:00,2025-04-15%2009:52:00'):

    url_ori = "https://%s/jetlinks/device-instance/%s/reportProperty" % (IP,DeviceID)
    url = "%s?terms[0].column=type$IN&terms[0].value=reportProperty&terms[1].value=%s&terms[2].column=deviceId&terms[2].value=%s&sorts[0].name=timestamp&sorts[0].order=desc&terms[1].column=timestamp$BTW" %(url_ori,datatime,DeviceID)

    payload={}

    timestamp = str(round(time.time() * 1000))
    SecureKey='kabGG3yFifKd4pXQKzNE3PBt'
    XSign=str(timestamp+SecureKey)
    md=hashlib.md5(XSign.encode())
    md5pwd=md.hexdigest()

    headers = {
       'X-Client-Id': 'iGxr2etHdDRXTxSZ',
       'X-Timestamp': timestamp,
       'X-Sign': md5pwd
    }

    response = requests.request("POST", url, headers=headers, data=payload, verify=False)

    return response.text

