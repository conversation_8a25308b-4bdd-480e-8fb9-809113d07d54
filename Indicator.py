import numpy as np
import math
from detecta import detect_peaks


#加权波动幅度：0.3N小+0.4N中+0.6N大，界限额定功率0.03，0.15,0.3
def WeightedFluctuation(s,weight_small=0.3,weight_middle=0.4,weight_large=0.6,threshold1=0.03,threshold2=0.15,threshold3=0.3):
    length=np.size(s)
    peaks=detect_peaks(s,edge='both', kpsh=False, valley=False, show=False, ax=None, title=True)
    valleys=detect_peaks(s,edge='both', kpsh=False, valley=True, show=False, ax=None, title=True)
    max_s=max(s)
    #峰谷位置
    peak_and_valley=np.zeros_like(s)
    for i in range(np.size(peaks)):
        peak_and_valley[peaks[i]]=1
    for i in range(np.size(valleys)):
        peak_and_valley[valleys[i]]=2
    #峰谷值
    peak_and_valley_index=np.zeros(np.size(peaks)+np.size(valleys))
    peak_and_valley_value=np.zeros(np.size(peaks)+np.size(valleys))
    index=0
    for i in range(length):
        if peak_and_valley[i] != 0:
            peak_and_valley_index[index]=i
            peak_and_valley_value[index]=s[i]
            index=index+1
    #加权波动幅值
    num_large=0
    num_middle=0
    num_small=0
    for i in range(np.size(peaks)+np.size(valleys)-1):
        if abs(peak_and_valley_value[i+1]-peak_and_valley_value[i])>=threshold3 and abs(peak_and_valley_value[i+1]-peak_and_valley_value[i])<max_s*0.85:
            num_large+=1
        elif abs(peak_and_valley_value[i+1]-peak_and_valley_value[i])>=threshold2 and abs(peak_and_valley_value[i+1]-peak_and_valley_value[i])<threshold3:
            num_middle+=1
        elif abs(peak_and_valley_value[i+1]-peak_and_valley_value[i])>=threshold1 and abs(peak_and_valley_value[i+1]-peak_and_valley_value[i])<threshold2:
            num_small+=1
    weighted_fluctuate=(weight_small*num_small+weight_middle*num_middle+weight_large*num_large)/10

    return  weighted_fluctuate

def Skew(s,middle_time):
    s_left=s[:middle_time]
    s_right=s[middle_time:]
    power_left=np.sum(s_left)
    power_right=np.sum(s_right)
    skew=power_left-power_right
    return skew/10



def Indicator_first(s1,s2):
    #均值
    mean_s1=np.mean(s1)
    mean_s2=np.mean(s2)
    
    max_s1=np.max(s1)
    max_s2=np.max(s2)
   
    indicator_1=math.sqrt(mean_s1**2+max_s1**2)
    indicator_2=math.sqrt(mean_s2**2+max_s2**2)
    
    indicator=abs(indicator_1-indicator_2)

    return indicator

#线距离----指标1：加权波动系数 指标2：0.8-平均值
def Indicator_second(s1,s2):
    #均值
    mean_s1=np.mean(s1)
    mean_s2=np.mean(s2)

  
    #加权波动幅度：0.3N小+0.4N中+0.6N大，界限额定功率0.01，0.15,0.3
    weighted_fluctuate_s1=3.5*WeightedFluctuation(s1)
    weighted_fluctuate_s2=3.5*WeightedFluctuation(s2)

    indicator_1=math.sqrt((0.8-mean_s1)**2+weighted_fluctuate_s1**2)
    indicator_2=math.sqrt((0.8-mean_s2)**2+weighted_fluctuate_s2**2)
    indicator=abs(indicator_1-indicator_2)
   
    return indicator

def Indicator_two(s1,s2,middle_time):
   
    st_s1=Skew(s1,middle_time)
    st_s2=Skew(s2,middle_time)
    weighted_fluctuate_s1=WeightedFluctuation(s1)
    weighted_fluctuate_s2=WeightedFluctuation(s2)

    _indicator_1=math.acos(st_s1/math.sqrt(st_s1**2+weighted_fluctuate_s1**2))
    _indicator_2=math.acos(st_s2/math.sqrt(st_s2**2+weighted_fluctuate_s2**2))
    indicator=abs(_indicator_1-_indicator_2)

    return indicator

#角距离----指标1：线距离 指标2：自定义偏度
def Indicator_third(s1,s2,middle_time):
    
    st_s1=Skew(s1,middle_time)
    st_s2=Skew(s2,middle_time)

    #加权波动幅度：0.3N小+0.4N中+0.6N大，界限额定功率0.01，0.15,0.3
    mean_s1=np.mean(s1)
    mean_s2=np.mean(s2)
    weighted_fluctuate_s1=WeightedFluctuation(s1)
    weighted_fluctuate_s2=WeightedFluctuation(s2)
    indicator_1=math.sqrt((0.8-mean_s1)**2+weighted_fluctuate_s1**2)
    indicator_2=math.sqrt((0.8-mean_s2)**2+weighted_fluctuate_s2**2)
    
    _indicator_1=math.acos(st_s1/math.sqrt(st_s1**2+indicator_1**2))
    _indicator_2=math.acos(st_s2/math.sqrt(st_s2**2+indicator_2**2))

    indicator=abs(_indicator_1-_indicator_2)
    return indicator



#时空波动系数
def SpaceTimeFluctuation(s,weight_small=0.3,weight_middle=0.4,weight_large=0.6,threshold1=0.03,threshold2=0.15,threshold3=0.3):
    length=np.size(s)
    peaks=detect_peaks(s,edge='both', kpsh=False, valley=False, show=False, ax=None, title=True)
    valleys=detect_peaks(s,edge='both', kpsh=False, valley=True, show=False, ax=None, title=True)
    #峰谷判断
    peak_and_valley=np.zeros_like(s)
    for i in range(np.size(peaks)):
        peak_and_valley[peaks[i]]=1
    for i in range(np.size(valleys)):
        peak_and_valley[valleys[i]]=2
    #峰谷值
    peak_and_valley_index=np.zeros(np.size(peaks)+np.size(valleys))
    peak_and_valley_value=np.zeros(np.size(peaks)+np.size(valleys))
    index=0
    for i in range(length):
        if peak_and_valley[i] != 0 :
            peak_and_valley_index[index]=i
            peak_and_valley_value[index]=s[i]
            index=index+1
    #加权波动幅值,六位数表示，12左，34中，56右
    num_large_l=0
    num_middle_l=0
    num_small_l=0
    num_large_m=0
    num_middle_m=0
    num_small_m=0
    num_large_r=0
    num_middle_r=0
    num_small_r=0
    for i in range(np.size(peaks)+np.size(valleys)-1):
        if abs(peak_and_valley_value[i+1]-peak_and_valley_value[i])>=threshold3:
            if peak_and_valley_index[i+1]>75 and peak_and_valley_index[i+1]<150:
                num_large_l+=1
            if peak_and_valley_index[i+1]>112 and peak_and_valley_index[i+1]<188:
                num_large_m+=1
            if peak_and_valley_index[i+1]>150 and peak_and_valley_index[i+1]<225:
                num_large_r+=1
        elif abs(peak_and_valley_value[i+1]-peak_and_valley_value[i])>=threshold2:
            if peak_and_valley_index[i+1]>75 and peak_and_valley_index[i+1]<150:
                num_middle_l+=1
            if peak_and_valley_index[i+1]>112 and peak_and_valley_index[i+1]<188:
                num_middle_m+=1
            if peak_and_valley_index[i+1]>150 and peak_and_valley_index[i+1]<225:
                num_middle_r+=1
        elif abs(peak_and_valley_value[i+1]-peak_and_valley_value[i])>=threshold1:
            if peak_and_valley_index[i+1]>75 and peak_and_valley_index[i+1]<150:
                num_small_l+=1
            if peak_and_valley_index[i+1]>112 and peak_and_valley_index[i+1]<188:
                num_small_m+=1
            if peak_and_valley_index[i+1]>150 and peak_and_valley_index[i+1]<225:
                num_small_r+=1
    
    fluctuate_l=weight_large*num_large_l+weight_middle*num_middle_l+weight_small*num_small_l
    fluctuate_m=weight_large*num_large_m+weight_middle*num_middle_m+weight_small*num_small_m
    fluctuate_r=weight_large*num_large_r+weight_middle*num_middle_r+weight_small*num_small_r
    
    return  fluctuate_l,fluctuate_m,fluctuate_r