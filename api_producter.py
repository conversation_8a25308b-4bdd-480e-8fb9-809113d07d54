# -*- coding: utf-8 -*-
"""
光伏出力预测API服务提供端
基于Tornado框架的Web API服务，提供光伏功率预测相关功能
"""

# 抑制PyTorch相关警告
import os
os.environ['NNPACK_DISABLE'] = '1'
os.environ['TORCH_CPP_LOG_LEVEL'] = 'ERROR'

# 导入功能模块
from Three_stage_cluster_1 import Three_stage_cluster        # 三阶段聚类模块
from Model_train_2 import Model_train                        # 模型训练模块
from Weather_error_generate_3 import Weather_error_generate  # 天气误差生成模块
from One_level_output_4 import One_level_output             # 一级输出模块
from Two_level_output_5 import Two_level_output             # 二级输出模块

# 导入标准库和第三方库
import argparse                                              # 命令行参数解析
import logging                                               # 日志记录
import json                                                  # JSON数据处理
import warnings                                              # 警告处理
import yaml                                                  # YAML配置文件处理

# 导入Tornado Web框架相关模块
from tornado import gen, ioloop, web                         # Tornado核心模块
from tornado.concurrent import run_on_executor               # 异步执行器装饰器
from concurrent.futures import ThreadPoolExecutor            # 线程池执行器
import nest_asyncio                                          # 嵌套异步IO支持

# 忽略警告信息
warnings.filterwarnings("ignore")

# 应用嵌套异步IO支持
nest_asyncio.apply()

class BaseRequestHandler(web.RequestHandler):
    """
    请求基类
    
    提供通用的请求处理功能，包括：
    - 请求名称获取
    - 安全令牌验证
    - 异步函数调用
    """
    executor = ThreadPoolExecutor(8)  # 线程池执行器，最大8个线程
    
    def get_request_name(self):
        """
        从请求头中获取请求名称
        
        Returns:
            str: 请求名称，默认为"Unknown"
        """
        head = self.request.headers  # 获取请求头部信息
        request_name = head.get("request-name", "Unknown")  # 从头部信息中获取操作名称，若不存在返回Unknown
        logging.info("[{}]".format(request_name))  # 记录请求名称到日志
        return request_name

    def security_token_check(self):
        """
        安全令牌验证
        
        验证请求头中的security-token是否正确
        
        Returns:
            bool: 验证通过返回True，否则返回False并响应错误信息
        """
        head = self.request.headers  # 获取请求头部信息
        head_security_token = head.get("security-token", None)  # 尝试从头部信息中获取security-token
        
        # 检查令牌是否匹配
        if head_security_token != security_token:
            # 构造认证失败的响应结果
            json_results = {
                "success": False,
                "successResult": None,
                "errMsg": "algorithm server auth failed."
            }
            self.write(json_results)  # 返回错误响应
            self.finish()             # 结束请求处理
            return False
        return True  # 认证通过

    @run_on_executor
    def call_func(self, request_name, function, input_dict):
        """
        异步功能调用
        
        利用线程池实现异步非阻塞的函数调用
        
        Args:
            request_name (str):     请求名称
            function (callable):    要调用的函数
            input_dict (dict):      输入参数字典
            
        Returns:
            dict: 包含执行结果的JSON格式字典
        """
        try:
            # 执行函数调用并构造成功响应结果
            json_results = {
                "success": True,                    # 执行成功标志
                "reques name": request_name,        # 请求名称（注意：这里有拼写错误，应为request）
                "result": function(**input_dict),   # 函数执行结果
                "errMsg": ""                        # 错误信息为空
            }
        except Exception as cause:
            # 捕获异常并构造失败响应结果
            json_results = {
                "success": False,                   # 执行失败标志
                "reques name": request_name,        # 请求名称
                "result": "",                       # 结果为空
                "errMsg": str(cause)                # 异常信息转换为字符串
            }
            logging.error("[{}] - error: {}".format(request_name, cause))  # 记录错误日志
        
        logging.info("[{}] - output: {}".format(request_name, json_results))  # 记录输出日志
        return json_results

class Health(web.RequestHandler):
    """
    健康检查服务
    
    提供API服务的健康状态检查接口
    """

    def get(self):
        """
        GET请求处理
        
        返回服务健康状态
        """
        self.write("OK!")  # 返回健康状态响应
    
class TrainService(BaseRequestHandler):
    """
    模型训练服务
    
    提供光伏预测模型相关的训练功能，包括：
    - 天气聚类
    - 神经网络参数训练  
    - 天气误差分布估计
    """

    def get(self):
        """
        GET请求处理
        
        返回训练服务状态信息
        """
        self.get_request_name()  # 获取并记录请求名称
        self.write("Train service.")  # 返回训练服务状态信息

    @gen.coroutine
    def post(self):
        """
        POST请求处理
        
        根据请求名称执行相应的训练功能
        支持的请求类型：
        - "Weather cluster": 天气聚类
        - "NN param train": 神经网络参数训练
        - "Weather error distribution estimate": 天气误差分布估计
        """
        request_name = self.get_request_name()  # 获取请求名称

        # 安全验证：检查是否启用拦截以及令牌验证
        if not enable_interception or self.security_token_check():
            input_dict = json.loads(self.request.body)  # 解析请求体中的JSON数据
            
            # 根据请求名称分发到相应的处理函数
            if request_name == "Weather cluster":
                # 执行天气聚类功能
                results = yield self.call_func(request_name, Three_stage_cluster, input_dict)
                self.write(results)  # 返回结果
                self.finish()        # 完成请求处理
            elif request_name == "NN param train":
                # 执行神经网络参数训练功能
                results = yield self.call_func(request_name, Model_train, input_dict)
                self.write(results)  # 返回结果
                self.finish()        # 完成请求处理
            elif request_name == "Weather error distribution estimate":
                # 执行天气误差分布估计功能
                results = yield self.call_func(request_name, Weather_error_generate, input_dict)
                self.write(results)  # 返回结果
                self.finish()        # 完成请求处理
            else:
                # 请求名称不匹配时返回错误信息
                self.write("Request name wrong! Please input 'Weather cluster' 'NN param train' or 'Weather error distribution estimate'")
                self.finish()
        else:
            # 认证失败时返回错误信息
            self.write("Authentication failed!")
            self.finish()

class OutputService(BaseRequestHandler):
    """
    光伏出力预测服务
    
    提供光伏功率预测输出功能，包括：
    - 一级输出：基于历史数据的自动预测
    - 二级输出：基于用户指定天气类型的预测
    """

    def get(self):
        """
        GET请求处理
        
        返回输出服务状态信息
        """
        self.get_request_name()  # 获取并记录请求名称
        self.write("Output service.")  # 返回输出服务状态信息

    @gen.coroutine
    def post(self):
        """
        POST请求处理
        
        根据请求名称执行相应的预测功能
        支持的请求类型：
        - "One level output": 一级输出（自动天气类型判断）
        - "Two level output": 二级输出（指定天气类型）
        """
        request_name = self.get_request_name()  # 获取请求名称

        # 安全验证：检查是否启用拦截以及令牌验证
        if not enable_interception or self.security_token_check():
            input_dict = json.loads(self.request.body)  # 解析请求体中的JSON数据
            
            # 根据请求名称分发到相应的处理函数
            if request_name == "One level output":
                # 执行一级输出功能（自动天气类型判断）
                results = yield self.call_func(request_name, One_level_output, input_dict)
                self.write(results)  # 返回结果
                self.finish()        # 完成请求处理
            elif request_name == "Two level output":
                # 执行二级输出功能（指定天气类型）
                results = yield self.call_func(request_name, Two_level_output, input_dict)
                self.write(results)  # 返回结果
                self.finish()        # 完成请求处理
            else:
                # 请求名称不匹配时返回错误信息
                self.write("Request name wrong! Please input 'One level output' or 'Two level output'")
                self.finish()
        else:
            # 认证失败时返回错误信息
            self.write("Authentication failed!")
            self.finish()


# ========== 主程序入口 ==========
if __name__ == "__main__":
    # ========== 日志配置 ==========
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # ========== 命令行参数解析 ==========
    parser = argparse.ArgumentParser(description="光伏出力预测API服务")
    parser.add_argument("--config_file", "-cfg", type=str, 
                       default='./configs/conf.json',
                       help="配置文件路径")
    args = parser.parse_args()
    
    # ========== 配置文件加载 ==========
    config_file = args.config_file  # 获取配置文件路径
    
    config_kwargs = {}  # 初始化配置参数字典
    with open(config_file, "r", encoding='utf-8') as f:  # 打开配置文件
        config = json.load(f)     # 使用JSON加载配置内容

    # 从配置文件中读取关键参数
    security_token = config["security"]              # 安全令牌
    enable_interception = config["interception"]     # 是否启用token认证拦截
    
    # ========== 启动信息打印 ==========
    import os
    import datetime
    
    logging.info("=" * 60)
    logging.info("光伏出力预测API服务启动中...")
    logging.info("=" * 60)
    logging.info(f"启动时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info(f"进程ID (PID): {os.getpid()}")
    logging.info(f"配置文件: {config_file}")
    logging.info(f"监听端口: {config['port']}")
    logging.info(f"安全认证: {'启用' if enable_interception else '禁用'}")
    logging.info(f"工作目录: {os.getcwd()}")
    logging.info("=" * 60)
    
    # ========== Web应用配置 ==========
    app = web.Application([
        (r"/api/health", Health),                   # 健康检查接口
        (r"/api/output_service", OutputService),    # 光伏出力预测服务接口
        (r"/api/train_service", TrainService),      # 模型训练服务接口
    ])

    # ========== 启动服务 ==========
    try:
        app.listen(config["port"])  # 监听指定端口
        
        # 启动成功信息
        logging.info("🚀 服务启动成功!")
        logging.info(f"📡 服务地址: http://localhost:{config['port']}")
        logging.info("📋 可用接口:")
        logging.info(f"   - GET  http://localhost:{config['port']}/api/health")
        logging.info(f"   - POST http://localhost:{config['port']}/api/output_service")
        logging.info(f"   - POST http://localhost:{config['port']}/api/train_service")
        logging.info("=" * 60)
        logging.info("💡 管理命令:")
        logging.info(f"   查看进程: ps aux | grep {os.getpid()}")
        logging.info(f"   停止服务: kill {os.getpid()}")
        logging.info(f"   强制停止: kill -9 {os.getpid()}")
        logging.info(f"   查看端口: lsof -i:{config['port']}")
        logging.info("=" * 60)
        logging.info("⌨️  按 Ctrl+C 停止服务")
        logging.info("📈 服务运行中，等待请求...")
        
        ioloop.IOLoop.instance().start()  # 启动Tornado事件循环
    except (KeyboardInterrupt, SystemExit) as cause:
        logging.info("🛑 接收到停止信号，正在关闭服务...")
        logging.info(f"📊 服务运行时长: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logging.info("✅ 服务已安全停止")
    except Exception as e:
        logging.error(f"❌ 服务启动失败: {e}")
        logging.error("请检查端口是否被占用或配置是否正确")