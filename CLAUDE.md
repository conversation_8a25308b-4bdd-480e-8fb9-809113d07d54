# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

重点：所有回复使用中文

## 项目概述

这是一个基于贝叶斯神经网络的光伏出力预测API服务系统，采用三阶段天气聚类和不确定性评估方法。

## 核心架构

### 三层预测体系
1. **模型训练层** (半年执行一次)
   - `Three_stage_cluster_1.py` - 三阶段天气聚类：根据历史气象数据将天气模式分类
   - `Model_train_2.py` - 神经网络模型训练：训练BP、LSTM、CNN三种贝叶斯网络
   - `Weather_error_generate_3.py` - 天气误差分布估计：建立误差概率模型

2. **预测服务层** (API后台逻辑)
   - `One_level_output_4.py` - 一级输出：基于历史数据自动判断天气类型并预测
   - `Two_level_output_5.py` - 二级输出：基于用户指定天气类型进行预测

3. **API接口层**
   - `api_producter.py` - 主API服务入口，基于Tornado框架

### 核心模块依赖关系
- **贝叶斯神经网络**：`Bayes_bp.py`, `Bayes_lstm.py`, `Bayes_cnn.py` 
- **数据处理**：`Data_process_zero.py`, `Data_process_one.py`, `Data_process_two.py`
- **评估指标**：`Indicator.py`, `Result_evaluate.py`
- **数据库连接**：`database_connect.py`
- **API消费者**：`api_consumer.py`

## 常用命令

### 服务管理（推荐使用）
```bash
# 启动服务
./service_manager.sh start

# 查看服务状态
./service_manager.sh status

# 查看服务日志
./service_manager.sh logs

# 停止服务
./service_manager.sh stop

# 重启服务
./service_manager.sh restart
```

### 直接启动方式
```bash
# 使用虚拟环境启动
./venv/bin/python api_producter.py

# 指定配置文件启动
python api_producter.py --config_file ./configs/conf.json

# 直接启动
python api_producter.py
```

### 环境依赖安装
```bash
pip install tornado pyyaml nest-asyncio requests urllib3 numpy pandas torch scipy scikit-learn matplotlib seaborn psycopg2-binary "numpy<2" blitz-bayesian-pytorch
```

### 健康检查
```bash
curl -X GET http://localhost:8081/api/health
```

## 开发关键信息

### 配置文件位置
- 主配置：`configs/conf.json`
- 默认端口：8081
- 认证：通过security-token验证

### 数据存储结构
- 电站数据：`database/[电站名称]/`
  - 原始数据：`PV data/`
  - 聚类结果：`cluster data/`
  - 训练模型：`train data/`
  - 预测结果：`output result with one-level input/`, `output result with two-level input/`
  - 误差数据：`weather error data/`

### 支持的天气类型
- 基础类型：["阴天", "多云", "晴天", "晴转多云", "多云转晴"]
- 系统通过三阶段聚类自动识别天气模式

### API请求格式
所有POST请求必须包含：
- `Content-Type: application/json`
- `request-name: [One level output|Two level output|Weather cluster|NN param train|Weather error distribution estimate]`
- `security-token: [配置文件中的令牌]`

### 重要技术特点
- 使用PyTorch和blitz-bayesian-pytorch实现贝叶斯神经网络
- 支持不确定性量化和置信区间估计
- 采用Monte Carlo方法进行概率预测
- 集成三种神经网络架构（BP、LSTM、CNN）进行集成学习

### 数据库支持
- PostgreSQL连接通过`database_connect.py`管理
- 支持时间序列数据插入和评估结果存储

### 注意事项
- 必须使用numpy<2版本以确保PyTorch兼容性
- 服务启动时会自动设置环境变量抑制NNPACK警告
- 虚拟环境位于`venv/`目录下

### 日志和监控
- 服务日志：`api_service.log`
- 进程PID：`api_service.pid`
- 支持实时日志查看：`tail -f api_service.log`