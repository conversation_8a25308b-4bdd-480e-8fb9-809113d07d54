import psycopg2
import time
from datetime import datetime

import psycopg2

def time_series_data_insert(database="arithmetic", 
                        user = "user_arithmetic", 
                        password = "dkdDfk@d3e9vPvd8f", 
                        host = "*************", 
                        port = "5866",
                        time_cycle = 3,
                        project_id = 652965,
                        project_name = 'ynfq',
                        user_id = 1,
                        time_flag = None,
                        predict_mean = None,
                        predict_up = None,
                        predict_low = None,
                        actual_output = None):

    conn = psycopg2.connect(database= database, 
                            user = user, 
                            password =password, 
                            host = host, 
                            port = port)

    cur = conn.cursor()

    for i in range(len(time_flag)):
        cur.execute("SELECT id from tgeem_oe_power_prediction")
        rows = cur.fetchall()
        if rows==[]:
            id = 1
        else :
            cur.execute("SELECT * FROM tgeem_oe_power_prediction WHERE id = (SELECT MAX(id) FROM tgeem_oe_power_prediction)")
            rows = cur.fetchall()
            id = id = rows[-1][0]+1
        #time_str = datetime.fromtimestamp(time_flag[i]/1e6).strftime('%Y-%m-%d %H:%M:%S')
        time_str = time_flag[i]
        cur.execute("SELECT * FROM tgeem_oe_power_prediction WHERE granularity_time = %s and time_cycle = %s", (time_str,time_cycle))
        rows = cur.fetchall()
        if rows!=[]:
            cur.execute("DELETE FROM tgeem_oe_power_prediction WHERE granularity_time = %s and time_cycle = %s", (time_str,time_cycle))
            id = rows[-1][0]
        cur.execute("INSERT INTO tgeem_oe_power_prediction (id, time_cycle, project_id, project_name, granularity_time, generating_output, output_upper_bound," \
            "output_lower_bound, actual_output, create_user, create_time, update_user, update_time) \
              VALUES ( %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)",
              (id, time_cycle,project_id,project_name,time_str,predict_mean[i],predict_up[i],predict_low[i], actual_output[i] if actual_output!=None else 0,
               user_id,time.strftime('%Y-%m-%d %H:%M:%S',time.localtime()),user_id,time.strftime('%Y-%m-%d %H:%M:%S',time.localtime())))

        conn.commit()
    conn.close()



def evaluation_data_insert(database="arithmetic", 
                        user = "user_arithmetic", 
                        password = "dkdDfk@d3e9vPvd8f", 
                        host = "*************", 
                        port = "5866",
                        time_cycle = 3,
                        project_id = 652965,
                        project_name = 'ynfq',
                        user_id = 1,
                        time_flag = None,
                        cover_rate = None,
                        distance_upandlow_ave = None,
                        distance_upandlow_max = None,
                        pinball_ave = None,
                        pinball_max = None,
                        daily_max_power_error_ave = None,
                        daily_max_power_error_max = None,
                        daily_ave_power_error_ave = None,
                        daily_ave_power_error_max = None,
                        eighty_percent_max_power_generation_time_error = None):

    conn = psycopg2.connect(database= database, 
                            user = user, 
                            password =password, 
                            host = host, 
                            port = port)

    cur = conn.cursor()
    cur.execute("SELECT id from tgeem_oe_power_evaluation")
    rows = cur.fetchall()
    if rows==[]:
        id = 1
    else :
        cur.execute("SELECT * FROM tgeem_oe_power_evaluation WHERE id = (SELECT MAX(id) FROM tgeem_oe_power_evaluation)")
        rows = cur.fetchall()
        id = id = rows[-1][0]+1
    # time_begin = datetime.fromtimestamp(time_flag[0]/1e6).strftime('%Y-%m-%d %H:%M:%S')
    # time_end = datetime.fromtimestamp(time_flag[-1]/1e6).strftime('%Y-%m-%d %H:%M:%S')
    time_begin = time_flag[0]
    time_end = time_flag[-1]
    cur.execute("SELECT * FROM tgeem_oe_power_evaluation WHERE granularity_time_begin = %s and granularity_time_end = %s and time_cycle = %s", (time_begin,time_end,time_cycle))
    rows = cur.fetchall()
    if rows!=[]:
        cur.execute("DELETE FROM tgeem_oe_power_evaluation WHERE granularity_time_begin = %s and granularity_time_end = %s and time_cycle = %s", (time_begin,time_end,time_cycle))
        id = rows[-1][0]
    cur.execute("INSERT INTO tgeem_oe_power_evaluation (id, time_cycle, project_id, project_name, granularity_time_begin, granularity_time_end," \
        " cover_rate, distance_upandlow_ave, distance_upandlow_max, pinball_ave, pinball_max, daily_max_power_error_ave, daily_max_power_error_max," \
        "daily_ave_power_error_ave, daily_ave_power_error_max, eighty_percent_max_power_generation_time_error, " \
            "create_user, create_time, update_user, update_time) \
              VALUES ( %s, %s, %s, %s, %s, %s, "
              "%s, %s, %s, %s, %s, %s, "
              "%s, %s, %s, %s,"
              "%s, %s, %s, %s)",
              (id, time_cycle,project_id,project_name,time_begin,time_end,
               cover_rate, distance_upandlow_ave, distance_upandlow_max, pinball_ave, pinball_max, daily_max_power_error_ave,
               daily_max_power_error_max, daily_ave_power_error_ave, daily_ave_power_error_max, eighty_percent_max_power_generation_time_error,
               user_id,time.strftime('%Y-%m-%d %H:%M:%S',time.localtime()),user_id,time.strftime('%Y-%m-%d %H:%M:%S',time.localtime())))
   
    conn.commit()
    conn.close()



def weather_type_data_insert(database="arithmetic", 
                        user = "user_arithmetic", 
                        password = "dkdDfk@d3e9vPvd8f", 
                        host = "*************", 
                        port = "5866",
                        time_cycle = 3,
                        project_id = 652965,
                        project_name = 'ynfq',
                        user_id = 1,
                        time_flag = None,
                        weather_type = None):


    conn = psycopg2.connect(database= database, 
                            user = user, 
                            password =password, 
                            host = host, 
                            port = port)

    cur = conn.cursor()
    for i in range(len(time_flag)):
        cur.execute("SELECT id from tgeem_oe_weather_type")
        rows = cur.fetchall()
        if rows==[]:
            id = 1
        else :
            cur.execute("SELECT * FROM tgeem_oe_weather_type WHERE id = (SELECT MAX(id) FROM tgeem_oe_weather_type)")
            rows = cur.fetchall()
            id = id = rows[-1][0]+1
        #time_str = datetime.fromtimestamp(time_flag[i]/1e6).strftime('%Y-%m-%d %H:%M:%S')
        time_str = time_flag[i]
        cur.execute("SELECT * FROM tgeem_oe_weather_type WHERE granularity_time = %s and time_cycle = %s", (time_str,time_cycle))
        rows = cur.fetchall()
        if rows!=[]:
            id = rows[-1][0]
            cur.execute("DELETE FROM tgeem_oe_weather_type WHERE granularity_time = %s and time_cycle = %s", (time_str,time_cycle))
        cur.execute("INSERT INTO tgeem_oe_weather_type (id, time_cycle, project_id, project_name, granularity_time,weather_type," \
            "create_user, create_time, update_user, update_time) \
              VALUES (%s, %s, %s, %s, %s, %s,"
              "%s, %s, %s, %s)",
              (id, time_cycle,project_id,project_name,time_str,weather_type[i],
               user_id,time.strftime('%Y-%m-%d %H:%M:%S',time.localtime()),user_id,time.strftime('%Y-%m-%d %H:%M:%S',time.localtime())))
        conn.commit()
    conn.close()

