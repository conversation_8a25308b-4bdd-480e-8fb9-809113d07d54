import pandas as pd
import numpy as np
from sklearn.decomposition import PCA
import scipy.signal as sgn
import sys
import json
from scipy.stats import pearsonr
from datetime import datetime

#相关性分析
def Relevancy(file_power,label_power,file_weather,label_weather):
    
    data=file_power
    power=np.zeros(len(data))
    for i in range(len(data)):
        power[i]=data[i]['properties'][label_power]
    power=np.reshape(power,(-1,1)).flatten()

    data=file_weather
    weather=np.zeros(len(data))
    for i in range(len(data)):
        weather[i]=data[i]['properties'][label_weather]
    weather=np.reshape(weather,(-1,1)).flatten()
    
    corr,p_value=pearsonr(power,weather) #Pearson系数计算
    if corr == "nan":
        corr = 0
    return corr

#主成分提取
def Weather_Factor(data,num_trait):
  data=np.reshape(data,(-1,num_trait))
  pca = PCA(n_components=1)
  pca.fit(data)
  weather_factor=pca.fit_transform(data)
  #print('ratio',pca.explained_variance_ratio_)#贡献度
  return weather_factor


#归一化
def Norm(data_original):
    data_norm=np.zeros_like(data_original)
    max_data=max(data_original.flatten())
    min_data=min(data_original.flatten())
    data_norm=(data_original-min_data)/(max_data-min_data)
    return data_norm,max_data,min_data

#滤波
def Savitzky(signal_original):
    signal_savitzky = sgn.savgol_filter(signal_original,53,9)
    return signal_savitzky



def WeatherDataProcess(granularity,path_weather,path_to,path_power,label_power,label_weather):

    #label_list_re=[]
    # for i in range(len(label_weather)):
    #     corr=Relevancy(path_power,label_power,path_weather,label_weather[i])
    #     if corr>=0.5:
    #         label_list_re.append(label_weather[i])
    label_list_re=label_weather
    
    dim=int(1440/granularity) #日出力曲线数据维度

    data = path_weather
    weather=np.zeros((len(data),len(label_list_re)))
    for i in range(len(data)):
        for j in range(len(label_list_re)):
            weather[i][j]=data[i]['properties'][label_list_re[j]]
    weather=np.reshape(weather,(-1,len(label_list_re)))
    weather_factor=Weather_Factor(weather,len(label_list_re))
    weather_factor=np.reshape(weather_factor,(-1,dim))
    num_sample=np.size(weather_factor,0)
    
    #标幺化（归一化）
    data_norm,max_data,min_data=Norm(weather_factor)
    weather_statistics={'max':max_data,'min':min_data}
    df = pd.DataFrame(list(weather_statistics.items()))
    df.to_csv(path_to,index=False,header=False)
    
    #滤波
    data_savitzky=np.zeros_like(data_norm)
    for i in range(num_sample):
        signal_original=data_norm[i]
        data_savitzky[i]=Savitzky(signal_original)
    data_savitzky=np.reshape(data_savitzky,(-1,dim))
   
    return data_savitzky,dim,num_sample,max_data,min_data



def NonNegative(data):
    num_sample=np.size(data,0) 
    dim=np.size(data,1) 
    for i in range(num_sample): #将出力负值剔除，赋值为0
        for j in range(dim):
            if data[i][j]<0:
                data[i][j]=0
    return data

def PowerDataPre(granularity,Capacity,excel_file_power,power_label):
    '''
    granularity:数据粒度,min
    Capacity:电站容量,kW
    '''
    dim=int(1440/granularity) #日出力曲线数据维度
    data_original=pd.read_excel(excel_file_power)
    power_label=data_original[power_label]
    power_label=power_label/Capacity #除上电站容量，标幺化
    data=np.reshape(power_label,(-1,dim))
    return data


def PowerDataProcess(granularity,Capacity,data,power_label):
    #功率标幺化

    dim=int(1440/granularity) #日出力曲线数据维度
    #data = json.loads(data)
    if len(data)%dim != 0:
        print(len(data)%dim)
        print("末端出力数据无法形成完整日出力曲线！请删除冗余数据或补充数据！")
        sys.exit(1)
    power=np.zeros(len(data))
    for i in range(len(data)):
        power[i]=data[i]['properties'][power_label]
    power=np.reshape(power,(-1,dim))
    power=power/Capacity #除上电站容量，标幺化
    num_sample=np.size(power,0) 
    
    #滤波
    data_savitzky=np.zeros_like(power)
    for i in range(num_sample):
        signal_original=power[i]
        data_savitzky[i]=Savitzky(signal_original)
    data_savitzky=np.reshape(data_savitzky,(-1,dim))
    
    #去除负值
    data_savitzky=NonNegative(data_savitzky)
    data_savitzky=np.reshape(data_savitzky,(-1,dim))
   
    return data_savitzky,dim,num_sample



def TimeDataProcess(granularity,data,time_label):
  
    dim=int(1440/granularity) #日出力曲线数据维度
    if len(data)%dim != 0:
        print(len(data)%dim)
        print("末端出力数据无法形成完整日出力曲线！请删除冗余数据或补充数据！")
        sys.exit(1)
    time=np.zeros(len(data))
    for i in range(len(data)):
        time_value = data[i][time_label]
        if time_value > 1e12:  # 粗略判断是否是毫秒级
            dt = datetime.fromtimestamp(time_value / 1000)
        else:
            dt = datetime.fromtimestamp(time_value)
        time[i] = np.datetime64(dt)
    time=np.reshape(time,(-1,dim))
    return time




