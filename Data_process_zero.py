from datetime import datetime
import bisect

def api_data_process(json_data, granularity=1, datatime='2025-04-14%2000:00:00,2025-04-15%2023:59:00'):
    # 将粒度转换为毫秒
    T = granularity * 60 * 1000  # 分钟转为毫秒

    # 解析datatime参数获取开始和结束时间
    try:
        # 处理时间字符串中的%20
        start_str, end_str = datatime.split(',')
        start_str = start_str.replace('%20', ' ')
        end_str = end_str.replace('%20', ' ')
        # 转换为datetime对象
        start_dt = datetime.strptime(start_str, "%Y-%m-%d %H:%M:%S")
        end_dt = datetime.strptime(end_str, "%Y-%m-%d %H:%M:%S")
        # 转换为毫秒时间戳
        start_ts = int(start_dt.timestamp() * 1000)
        end_ts = int(end_dt.timestamp() * 1000)
    except Exception as e:
        raise ValueError(f"Invalid datatime format: {e}")

    # 过滤并处理输入数据：只保留时间段内的数据，去重并按时间戳排序
    filtered_data = [item for item in json_data if start_ts <= item['timestamp'] <= end_ts]
    # 使用字典去重，保留每个时间戳最后一个出现的项
    unique_data = {}
    for item in filtered_data:
        unique_data[item['timestamp']] = item
    sorted_data = sorted(unique_data.values(), key=lambda x: x['timestamp'])
    timestamps = [item['timestamp'] for item in sorted_data]

    # 生成目标时间段内所有理论上的时间戳列表
    ts_list = []
    current_ts = start_ts
    while current_ts <= end_ts:
        ts_list.append(current_ts)
        current_ts += T

    # 为每个时间戳生成或获取对应的数据点
    result = []
    for ts in ts_list:
        # 使用二分查找确定插入位置
        idx = bisect.bisect_right(timestamps, ts)
        prev_idx = idx - 1

        # 确定用于填补的数据点
        if prev_idx >= 0:
            # 存在前驱数据点
            selected_item = sorted_data[prev_idx]
        elif sorted_data:
            # 没有前驱但数据存在，取第一个数据点
            selected_item = sorted_data[0]
        else:
            # 无数据可用，跳过（或根据需求抛出错误/填充默认值）
            continue

        # 创建新数据点（深拷贝避免修改原数据）
        new_item = {k: v for k, v in selected_item.items()}
        new_item['timestamp'] = ts
        result.append(new_item)

    return result