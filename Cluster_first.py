import numpy as np
import matplotlib.pyplot as plt
from Data_process_one import WeatherDataProcess,PowerDataProcess
import pandas as pd
from Kpp_fcm_first import KPPFCM
import os
import math
from api_consumer import data_post,split_time_intervals
from Data_process_zero import api_data_process
import json

plt.rcParams['font.sans-serif']=['SimHei']
plt.rcParams['axes.unicode_minus']=False
os.environ["OMP_NUM_THREADS"] = '1'

def cluster_first(IP='*************',
                  DeviceID_weather='652965_ynfq-1646999496414',
                  DeviceID_power='652965_ynfq-1646999496407',
                  datatime='2024-04-14%2000:00:00,2025-04-13%2023:59:00',
                  power_label='jd2p',
                  weather_label=['afzd','zfzd','sfzd','hwd','sd','fs','fx','yl'],
                  path_root="F:/SanXia/Generate_PV_api/database/大兴/cluster data/cluster_first",
                  capacity=40313.2,
                  granularity=1):
    """
    :param path_from_power:历史功率文件列表
    :param path_from_weather:历史气象文件列表
    :param path_root:聚类结果存储根目录
    :param power_label:功率标签
    :param weather_label:气象标签
    :param Capacity:电站实际上网容量
    :param granularity:数据间隔，单位分钟
    :return 阴天、多云、晴天等 三类的日气象曲线、日出力曲线以及日出力中心曲线的csv文件以及气象数据的最值
    """
    
    num_cluster=3
    iteration=100
    weight=2
    error=1e-4

    if not os.path.exists(path_root):
        os.makedirs(path_root)
    path_statistic_weather=os.path.join(path_root, f'weather_statistics.csv') #天气归一化数据存储路径

    split_data_time=split_time_intervals(datatime)

    #基于api得到输入数据
    path_from_weather=[]
    path_from_power=[]
    for i in range(len(split_data_time)):
        temp_weather=json.loads(data_post(IP=IP,DeviceID=DeviceID_weather,datatime=split_data_time[i]))
        path_from_weather.append(temp_weather['result'][0])
        temp_power=json.loads(data_post(IP=IP,DeviceID=DeviceID_power,datatime=split_data_time[i]))
        path_from_power.append(temp_power['result'][0])
        
       
    path_from_weather=api_data_process(path_from_weather,granularity)
    path_from_power=api_data_process(path_from_power,granularity)

    '''数据处理：去噪、标准化、归一化'''
    weather_factor,dim,num_sample,max_weather,min_weather=WeatherDataProcess(granularity,path_from_weather,path_statistic_weather,path_from_power,power_label,weather_label)
    data,dim,num_sample=PowerDataProcess(granularity,capacity,path_from_power,power_label)
    
    
    '''判断聚类数量,并进行聚类,聚类指标是最大值和均值的欧氏距离,判断指标是CH'''
    cluster_final,center_matrix_power_final,center_matrix_weather_final,object_final=KPPFCM(data,weather_factor,num_cluster,iteration,weight,error)

   
    '''保存聚类结果并输出'''
    power=[]
    weather=[]
    index=[0]
    num=0
    for k in range(num_cluster):
        for i in range(num_sample):
            if cluster_final[i]==k:
                num=num+1
                power.append(data[i]) 
                weather.append(weather_factor[i])
        index.append(num)

    power=np.array(power).reshape(-1,dim)
    weather=np.array(weather).reshape(-1,dim)
    
  
    '''计算类内指标均值，对该类的天气类型进行标记'''
    power_mean_max=[]
    mean=0
    for k in range(num_cluster):
        for i in range(index[k],index[k+1]):
            mean=mean+math.sqrt(np.mean(power[i,:])**2+np.max(power[i,:])**2)   
        mean=mean/(index[k+1]-index[k])
        power_mean_max.append(mean)

    '''自动创建目录来保存不同类的结果'''
    cluster_class=['阴天','多云','晴天等']
    color_list=['lightgreen','gold','deepskyblue','orchid','lightcoral','mediumorchid']
    weather_statistics=[]
    for i in range(len(power_mean_max)):
        classi_index=np.argmin(power_mean_max) ##0号文件是无波动的，1号文件是有波动的
        power_mean_max[classi_index]=1000
        path_power=os.path.join(path_root, f'power_{cluster_class[i]}.csv')
        path_weather=os.path.join(path_root, f'weather_{cluster_class[i]}.csv')
        path_center=os.path.join(path_root, f'center_{cluster_class[i]}.csv')
        power_temp=power[index[classi_index]:index[classi_index+1],:]
        weather_temp=weather[index[classi_index]:index[classi_index+1],:]
        df = pd.DataFrame(power_temp)
        df.to_csv(path_power,index=False,header=False)
        df = pd.DataFrame(weather_temp)
        df.to_csv(path_weather,index=False,header=False)
        cluster_center=np.vstack((np.reshape(center_matrix_weather_final[classi_index],(1,-1)),np.reshape(center_matrix_power_final[classi_index],(1,-1))))
        df = pd.DataFrame(cluster_center)
        df.to_csv(path_center,index=False,header=False)
   



if __name__=="__main__":
    cluster_first()

                