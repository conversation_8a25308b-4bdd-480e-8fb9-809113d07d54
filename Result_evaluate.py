import numpy as np

def CoverRate(curve_real,curve_up,curve_low): 
    C=0
    for i in range(curve_real.shape[0]):
        for j in range(curve_real.shape[1]):
            if curve_real[i,j]<=curve_up[i,j] and curve_real[i,j]>=curve_low[i,j]:
                C=C+1
    C=C/(curve_real.shape[0]*curve_real.shape[1])
    return C

def DistanceUpandLow(curve_up,curve_low):
    dis=curve_up-curve_low
    return np.mean(dis),np.max(dis)

def Pinball(curve_real,curve_up,curve_low):
    P=[]
    for i in range(curve_real.shape[0]):
        p=0
        for j in range(curve_real.shape[1]):
            if curve_real[i,j]<=curve_up[i,j] and curve_real[i,j]>=curve_low[i,j]:
                p=p+(1-0.9)*(curve_up[i,j]-curve_low[i,j])/28.8
            elif curve_real[i,j] > curve_up[i,j]:
                p=p+0.9*(curve_real[i,j]-curve_up[i,j])/28.8
            else :
                p=p+0.9*(curve_low[i,j]-curve_real[i,j])/28.8
        P.append(p)
    return np.mean(P),np.max(P)
def AccuracyPmax(curve_real,curve_sim):
    A=[]
    for i in range (curve_real.shape[0]):
        Pmax_ave_real=np.max(curve_real[i,:])
        Pmax_ave_sim=np.max(curve_sim[i,:])
        A.append(abs(Pmax_ave_real-Pmax_ave_sim)/Pmax_ave_real)
    A_ave=np.mean(A)
    A_max=np.max(A)
    return A_ave,A_max

def AccuracyPave(curve_real,curve_sim):
    A=[]
    for i in range (curve_real.shape[0]):
        Pave_ave_real=np.mean(curve_real[i,:])
        Pave_ave_sim=np.mean(curve_sim[i,:])
        A.append(abs(Pave_ave_real-Pave_ave_sim)/Pave_ave_real)
    A_ave=np.mean(A)
    A_max=np.max(A)
    return A_ave,A_max

def AccuracyT80(curve_real,curve_sim):
    T_real=0
    T_sim=0
    Pmax=np.max(curve_real)
    for i in range (curve_real.shape[0]):
        for j in range(curve_real.shape[1]):
            if curve_real[i,j]>=0.8*Pmax:
                T_real=T_real+1
            if curve_sim[i,j]>=0.8*Pmax:
                T_sim=T_sim+1
    
    A=abs(T_sim-T_real)/(T_real+1e-7)
    return A



    




