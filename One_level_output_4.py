# -*- coding: utf-8 -*-
"""
光伏出力一级输出模块
用于基于贝叶斯神经网络的光伏功率预测和不确定性评估
"""

# 导入必要的库
import numpy as np                # 数值计算库
import torch                      # PyTorch深度学习框架
import pandas as pd               # 数据处理库
import matplotlib.pyplot as plt   # 绘图库
import os                         # 操作系统接口
import json                       # JSON数据处理

# 导入自定义模块
from Bayes_bp import BpNetwork                                   # 贝叶斯BP神经网络
from Bayes_lstm import LstmRNN                                   # 贝叶斯LSTM神经网络
from Bayes_cnn import CNNNetwork                                 # 贝叶斯CNN神经网络
from Indicator import Indicator_first, Indicator_second, Indicator_third  # 气象类型判断指标
from scipy.stats import norm                                    # 正态分布统计函数
from Data_process_two import DataPre                            # 数据预处理模块
from Data_process_one import PowerDataProcess, WeatherDataProcess, TimeDataProcess, Savitzky  # 数据处理模块
from Result_evaluate import CoverRate, AccuracyPmax, AccuracyPave, AccuracyT80, Pinball, DistanceUpandLow  # 结果评估模块
from api_consumer import data_post, split_time_intervals         # API数据获取模块
from Data_process_zero import api_data_process                   # API数据处理模块
from database_connect import time_series_data_insert, evaluation_data_insert, weather_type_data_insert  # 数据库连接模块

# 设置matplotlib中文字体和负号显示
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置OpenMP线程数为1，避免多线程冲突
os.environ["OMP_NUM_THREADS"] = '1'

def One_level_output(IP='*************',
                     DeviceID_weather='652965_ynfq-1646999496414',
                     DeviceID_power='652965_ynfq-1646999496407',
                     datatime='2025-03-14%2000:00:00,2025-04-13%2023:59:00',
                     stage_name="大兴",
                     capacity=40313.2,
                     path_root="./database",
                     granularity=1,
                     confidence=0.95,
                     power_label='jd2p',
                     weather_label=['afzd', 'zfzd', 'sfzd', 'hwd', 'sd', 'fs', 'fx', 'yl'],
                     time_label='timestamp',
                     hidden_size=[256, 256, 64],
                     batch_size=1,
                     monte_carlo_num=20,
                     database="arithmetic",
                     user="user_arithmetic",
                     password="dkdDfk@d3e9vPvd8f",
                     host="*************",
                     port="5866",
                     time_cycle=3,
                     project_id=652965,
                     project_name='ynfq',
                     user_id=1
                     ):
    """
    光伏出力一级输出预测函数
    
    基于贝叶斯神经网络的光伏功率预测和不确定性评估系统
    使用历史气象数据和功率数据，结合机器学习模型进行光伏出力预测
    
    参数说明:
    :param IP: 历史数据存储库所在服务器IP地址
    :param DeviceID_weather: 气象数据设备ID
    :param DeviceID_power: 功率数据设备ID
    :param datatime: 所需生成数据对应时间戳
    :param stage_name: 电站名称
    :param capacity: 电站容量 (kW)
    :param path_root: 内置数据文件根目录
    :param granularity: 数据粒度，单位：分钟
    :param confidence: 置信度参数，实际置信度=2*confidence-1
    :param power_label: 功率数据标签
    :param weather_label: 气象数据标签列表
    :param time_label: 时间数据标签
    :param hidden_size: 神经网络隐藏层神经元个数 [bp, cnn, lstm]
    :param batch_size: 训练批大小
    :param monte_carlo_num: 蒙特卡洛采样次数
    :param database: 生成结果存储数据库名称
    :param user: 数据库登录用户名
    :param password: 数据库登录密码
    :param host: 数据库所在服务器IP地址
    :param port: 数据库所在服务器连接端口
    :param time_cycle: 生成数据时间跨度(1:日, 2:周, 3:月, 4:年)
    :param project_id: 电站对应ID
    :param project_name: 电站名称对应简写符号
    :param user_id: 存储操作用户编号
    
    返回值:
    生成给定电站和时段的光伏出力预测结果及评价结果
    """
    # ========== 基本参数设置 ==========
    path_root = os.path.join(path_root, stage_name)
    z = norm.ppf(confidence)                   # 置信度对应的z值
    TIME = int(1440 / granularity)             # 每日时间点数量
    INPUT_SIZE = TIME                          # 输入层大小
    OUTPUT_SIZE = TIME                         # 输出层大小
    HIDDEN_LSTM = hidden_size[2]               # LSTM隐藏层大小
    HIDDEN_CNN = hidden_size[1]                # CNN隐藏层大小
    HIDDEN_BP = hidden_size[0]                 # BP隐藏层大小
    BATCH_SIZE = batch_size                    # 批处理大小
    # ========== 测试数据路径和结果存储路径设置 ==========
    path_to = os.path.join(path_root, f"output result with one-level input")
    if not os.path.exists(path_to):
        os.makedirs(path_to)
    
    # 输出文件路径定义
    PATH_STATISTIC = os.path.join(path_to, f"weather_statistics.csv")          # 气象统计文件
    PATH_SIM_RESULT = os.path.join(path_to, f"simulation_result_{granularity}min.xlsx")  # 仿真结果文件
    PATH_EVA_RESULT1 = os.path.join(path_to, f"evaluation_result_weather_type.xlsx")     # 天气类型评估结果
    PATH_EVA_RESULT2 = os.path.join(path_to, f"evaluation_result_indicator.xlsx")        # 指标评估结果
    # ========== 模型参数路径设置 ==========
    path_train = os.path.join(path_root, f"train data")
    path_train_y = os.path.join(path_train, f"阴天")           # 阴天模型路径
    path_train_d = os.path.join(path_train, f"多云")           # 多云模型路径
    path_train_q = os.path.join(path_train, f"晴天")           # 晴天模型路径
    path_train_dq = os.path.join(path_train, f"多云转晴")      # 多云转晴模型路径
    path_train_qd = os.path.join(path_train, f"晴转多云")      # 晴转多云模型路径
    
    # 各天气类型对应的模型参数文件路径
    PATH_Y_BP = os.path.join(path_train_y, f'bp_model_params.pth')
    PATH_Y_LSTM = os.path.join(path_train_y, f'lstm_model_params.pth')
    PATH_Y_CNN = os.path.join(path_train_y, f'cnn_model_params.pth')
    PATH_D_BP = os.path.join(path_train_d, f'bp_model_params.pth')
    PATH_D_LSTM = os.path.join(path_train_d, f'lstm_model_params.pth')
    PATH_D_CNN = os.path.join(path_train_d, f'cnn_model_params.pth')
    PATH_Q_BP = os.path.join(path_train_q, f'bp_model_params.pth')
    PATH_Q_LSTM = os.path.join(path_train_q, f'lstm_model_params.pth')
    PATH_Q_CNN = os.path.join(path_train_q, f'cnn_model_params.pth')
    PATH_DQ_BP = os.path.join(path_train_dq, f'bp_model_params.pth')
    PATH_DQ_LSTM = os.path.join(path_train_dq, f'lstm_model_params.pth')
    PATH_DQ_CNN = os.path.join(path_train_dq, f'cnn_model_params.pth')
    PATH_QD_BP = os.path.join(path_train_qd, f'bp_model_params.pth')
    PATH_QD_LSTM = os.path.join(path_train_qd, f'lstm_model_params.pth')
    PATH_QD_CNN = os.path.join(path_train_qd, f'cnn_model_params.pth')
    # ========== 聚类中心曲线路径设置 ==========
    path_cluster = os.path.join(path_root, f"cluster data")
    path_cluster_first = os.path.join(path_cluster, f"cluster_first")
    path_cluster_second = os.path.join(path_cluster, f"cluster_second")
    path_cluster_third = os.path.join(path_cluster, f"cluster_third")
    
    # 各类天气类型聚类中心文件路径
    CENTER_Q1_PATH = os.path.join(path_cluster_first, f'center_晴天等.csv')
    CENTER_Q2_PATH = os.path.join(path_cluster_second, f'center_晴天.csv')
    CENTER_Y_PATH = os.path.join(path_cluster_first, f'center_阴天.csv')
    CENTER_D_PATH = os.path.join(path_cluster_first, f'center_多云.csv')
    CENTER_B_PATH = os.path.join(path_cluster_second, f'center_变化天气等.csv')
    CENTER_DQ_PATH = os.path.join(path_cluster_third, f'center_多云转晴.csv')
    CENTER_QD_PATH = os.path.join(path_cluster_third, f'center_晴转多云.csv')
    # ========== 数据处理：加载聚类中心数据 ==========
    # 加载各类天气的聚类中心曲线，用于气象类型判断
    center_qingtian1 = DataPre(granularity, CENTER_Q1_PATH).reshape(2, TIME)          # 晴天等聚类中心
    center_qingtian2 = DataPre(granularity, CENTER_Q2_PATH).reshape(2, TIME)          # 晴天聚类中心
    center_yintian = DataPre(granularity, CENTER_Y_PATH).reshape(2, TIME)             # 阴天聚类中心
    center_duoyun = DataPre(granularity, CENTER_D_PATH).reshape(2, TIME)              # 多云聚类中心
    center_bianhua = DataPre(granularity, CENTER_B_PATH).reshape(2, TIME)             # 变化天气等聚类中心
    center_duoyunzhuanqing = DataPre(granularity, CENTER_DQ_PATH).reshape(2, TIME)    # 多云转晴聚类中心
    center_qingzhuanduoyun = DataPre(granularity, CENTER_QD_PATH).reshape(2, TIME)    # 晴转多云聚类中心
    # ========== API数据获取与处理 ==========
    path_statistic_weather = PATH_STATISTIC
    
    # 分割时间间隔并获取数据
    split_data_time = split_time_intervals(datatime)
    path_from_weather = []
    path_from_power = []
    
    # 循环获取各时间段的气象和功率数据
    for i in range(len(split_data_time)):
        # 获取气象数据
        temp_weather = json.loads(data_post(IP=IP, DeviceID=DeviceID_weather, datatime=split_data_time[i]))
        temp_weather = api_data_process(temp_weather['result'][:], granularity, datatime=split_data_time[i])
        path_from_weather.extend(temp_weather)
        
        # 获取功率数据
        temp_power = json.loads(data_post(IP=IP, DeviceID=DeviceID_power, datatime=split_data_time[i]))
        temp_power = api_data_process(temp_power['result'][:], granularity, datatime=split_data_time[i])
        path_from_power.extend(temp_power)
    
    # 数据预处理
    data, dim, num_sample = PowerDataProcess(granularity, capacity, path_from_power, power_label)
    weather_factor, dim, num_sample, max_weather, min_weather = WeatherDataProcess(
        granularity, path_from_weather, path_statistic_weather, path_from_power, power_label, weather_label)
    time_flag = TimeDataProcess(granularity, path_from_power, time_label)
    
    # 气象数据归一化处理
    weather_norm = np.reshape(weather_factor, (num_sample, TIME))
    # ========== 气象类型判断 ==========
    # 初始化距离矩阵和天气类型数组
    weather_distance1 = np.zeros((weather_norm.shape[0], 3))    # 第一级分类距离
    weather_distance2 = np.zeros((weather_norm.shape[0], 2))    # 第二级分类距离
    weather_distance3 = np.zeros((weather_norm.shape[0], 2))    # 第三级分类距离
    weather_type = np.zeros(weather_norm.shape[0])              # 天气类型数组
    
    # 对每一天进行天气类型判断
    for day in range(weather_norm.shape[0]):
        # 第一级分类：阴天、多云、晴天等
        weather_distance1[day, 0] = Indicator_first(weather_norm[day, :], center_yintian[0, :])
        weather_distance1[day, 1] = Indicator_first(weather_norm[day, :], center_duoyun[0, :])
        weather_distance1[day, 2] = Indicator_first(weather_norm[day, :], center_qingtian1[0, :])
        
        # 如果最接近晴天等类型，进行第二级分类
        if weather_distance1[day, 2] == np.min(weather_distance1[day, :]):
            weather_distance2[day, 0] = Indicator_second(weather_norm[day, :], center_qingtian2[0, :])
            weather_distance2[day, 1] = Indicator_second(weather_norm[day, :], center_bianhua[0, :])
            
            # 如果最接近变化天气等，进行第三级分类
            if weather_distance2[day, 1] == np.min(weather_distance2[day, :]):
                weather_distance3[day, 0] = Indicator_third(weather_norm[day, :], center_duoyunzhuanqing[0, :], 165)
                weather_distance3[day, 1] = Indicator_third(weather_norm[day, :], center_qingzhuanduoyun[0, :], 165)
                weather_type[day] = 3 + np.argmin(weather_distance3[day, :])  # 多云转晴(3)或晴转多云(4)
            else:
                weather_type[day] = 2  # 晴天
        else:
            weather_type[day] = np.argmin(weather_distance1[day, :])  # 阴天(0)或多云(1)
    # ========== 光伏出力生成 ==========
    # 初始化各模型预测结果数组
    predictive_y_for_testing_lstm_mean = np.zeros((weather_norm.shape[0], weather_norm.shape[1]))
    predictive_y_for_testing_lstm_up = np.zeros((weather_norm.shape[0], weather_norm.shape[1]))
    predictive_y_for_testing_lstm_low = np.zeros((weather_norm.shape[0], weather_norm.shape[1]))
    predictive_y_for_testing_cnn_mean = np.zeros((weather_norm.shape[0], weather_norm.shape[1]))
    predictive_y_for_testing_cnn_up = np.zeros((weather_norm.shape[0], weather_norm.shape[1]))
    predictive_y_for_testing_cnn_low = np.zeros((weather_norm.shape[0], weather_norm.shape[1]))
    predictive_y_for_testing_bp_mean = np.zeros((weather_norm.shape[0], weather_norm.shape[1]))
    predictive_y_for_testing_bp_up = np.zeros((weather_norm.shape[0], weather_norm.shape[1]))
    predictive_y_for_testing_bp_low = np.zeros((weather_norm.shape[0], weather_norm.shape[1]))
    predict_mean = np.zeros((weather_norm.shape[0], weather_norm.shape[1]))
    predict_up = np.zeros((weather_norm.shape[0], weather_norm.shape[1]))
    predict_low = np.zeros((weather_norm.shape[0], weather_norm.shape[1]))

    # 对每一天的数据进行预测
    for day in range(weather_norm.shape[0]):
        # 准备测试数据
        test_x = weather_norm[day, :].astype('float32').reshape(1, weather_norm.shape[1])
        test_y = data[day, :]
        
        # 根据天气类型选择对应的模型路径
        if weather_type[day] == 0:        # 阴天
            PATH1 = PATH_Y_BP
            PATH2 = PATH_Y_LSTM
            PATH3 = PATH_Y_CNN
        elif weather_type[day] == 1:      # 多云
            PATH1 = PATH_D_BP
            PATH2 = PATH_D_LSTM
            PATH3 = PATH_D_CNN
        elif weather_type[day] == 2:      # 晴天
            PATH1 = PATH_Q_BP
            PATH2 = PATH_Q_LSTM
            PATH3 = PATH_Q_CNN
        elif weather_type[day] == 3:      # 多云转晴
            PATH1 = PATH_DQ_BP
            PATH2 = PATH_DQ_LSTM
            PATH3 = PATH_DQ_CNN
        else:                             # 晴转多云
            PATH1 = PATH_QD_BP
            PATH2 = PATH_QD_LSTM
            PATH3 = PATH_QD_CNN
        
        # 初始化蒙特卡洛采样结果数组
        std_error_mean_list = np.zeros((test_x.shape[0], test_x.shape[1], int(monte_carlo_num * 3)))
        # ========== LSTM模型预测 ==========
        predictive_y_for_testing_lstm = np.zeros((test_x.shape[0], test_x.shape[1], monte_carlo_num))
        for test_num in range(monte_carlo_num):
            # 加载LSTM模型并进行预测
            lstm_model = LstmRNN(n_input=INPUT_SIZE, n_hidden=HIDDEN_LSTM, n_output=OUTPUT_SIZE)
            lstm_model.load_state_dict(torch.load(PATH2))  # 从文件加载模型参数
            lstm_model = lstm_model.eval()  # 切换到测试模式
            test_x_tensor = test_x.reshape(-1, BATCH_SIZE, INPUT_SIZE)
            test_x_tensor = torch.from_numpy(test_x_tensor)
            predictive_y_for_testing_lstm[:, :, test_num] = lstm_model(test_x_tensor).view(-1, OUTPUT_SIZE).data.numpy()
            std_error_mean_list[:, :, test_num] = predictive_y_for_testing_lstm[:, :, test_num]
        
        # 计算LSTM预测结果的均值和置信区间
        predictive_y_for_testing_lstm_mean[day, :] = np.mean(predictive_y_for_testing_lstm, 2)
        std_error_lstm = np.std(predictive_y_for_testing_lstm, 2)
        predictive_y_for_testing_lstm_up[day, :] = predictive_y_for_testing_lstm_mean[day, :] + z * std_error_lstm
        predictive_y_for_testing_lstm_low[day, :] = predictive_y_for_testing_lstm_mean[day, :] - z * std_error_lstm

        # ========== CNN模型预测 ==========
        predictive_y_for_testing_cnn = np.zeros((test_x.shape[0], test_x.shape[1], monte_carlo_num))
        for test_num in range(monte_carlo_num):
            # 加载CNN模型并进行预测
            cnn_model = CNNNetwork(n_input=INPUT_SIZE, n_hidden=HIDDEN_CNN, n_output=OUTPUT_SIZE)
            cnn_model.load_state_dict(torch.load(PATH3))  # 从文件加载模型参数
            cnn_model = cnn_model.eval()  # 切换到测试模式
            predictive_y_for_testing_cnn[:, :, test_num] = cnn_model(test_x_tensor).view(-1, OUTPUT_SIZE).data.numpy()
            std_error_mean_list[:, :, monte_carlo_num + test_num] = predictive_y_for_testing_cnn[:, :, test_num]
        
        # 计算CNN预测结果的均值和置信区间
        predictive_y_for_testing_cnn_mean[day, :] = np.mean(predictive_y_for_testing_cnn, 2)
        std_error_res = np.std(predictive_y_for_testing_cnn, 2)
        predictive_y_for_testing_cnn_up[day, :] = predictive_y_for_testing_cnn_mean[day, :] + z * std_error_res
        predictive_y_for_testing_cnn_low[day, :] = predictive_y_for_testing_cnn_mean[day, :] - z * std_error_res

        # ========== BP神经网络模型预测 ==========
        predictive_y_for_testing_bp = np.zeros((test_x.shape[0], test_x.shape[1], monte_carlo_num))
        for test_num in range(monte_carlo_num):
            # 加载BP模型并进行预测
            bp_model = BpNetwork(n_input=INPUT_SIZE, n_hidden=HIDDEN_BP, n_output=OUTPUT_SIZE)
            bp_model.load_state_dict(torch.load(PATH1))  # 从文件加载模型参数
            bp_model = bp_model.eval()  # 切换到测试模式
            predictive_y_for_testing_bp[:, :, test_num] = bp_model(test_x_tensor).view(-1, OUTPUT_SIZE).data.numpy()
            std_error_mean_list[:, :, int(2 * monte_carlo_num + test_num)] = predictive_y_for_testing_bp[:, :, test_num]
        
        # 计算BP预测结果的均值和置信区间
        predictive_y_for_testing_bp_mean[day, :] = np.mean(predictive_y_for_testing_bp, 2)
        std_error_bp = np.std(predictive_y_for_testing_bp, 2)
        predictive_y_for_testing_bp_up[day, :] = predictive_y_for_testing_bp_mean[day, :] + z * std_error_bp
        predictive_y_for_testing_bp_low[day, :] = predictive_y_for_testing_bp_mean[day, :] - z * std_error_bp
        
        # ========== 集成模型结果计算 ==========
        # 计算三个模型的集成预测结果
        std_error_mean = np.std(std_error_mean_list, 2)
        predict_mean[day, :] = (predictive_y_for_testing_bp_mean[day, :] + 
                               predictive_y_for_testing_cnn_mean[day, :] + 
                               predictive_y_for_testing_lstm_mean[day, :]) / 3
        predict_up[day, :] = predict_mean[day, :] + z * std_error_mean
        predict_low[day, :] = predict_mean[day, :] - z * std_error_mean
    # ========== 后处理：滤波和非负性约束 ==========
    # 对预测结果进行Savitzky-Golay滤波，平滑处理
    for i in range(weather_norm.shape[0]):
        predict_mean[i] = Savitzky(predict_mean[i])
        predict_up[i] = Savitzky(predict_up[i])
        predict_low[i] = Savitzky(predict_low[i])
    
    # 确保预测下界非负（光伏出力不能为负）
    predict_low[predict_low < 0] = 0
    
    # 提取日期信息
    time_flag_day = time_flag[:, 0]
    time_flag_day = time_flag_day.flatten()
    # ========== 天气类型处理和存储 ==========
    # 将天气类型数字编码转换为中文字符串
    weather_type_string = []
    for i in range(num_sample):
        if weather_type[i] == 0:
            weather_type_string.append('阴天')
        elif weather_type[i] == 1:
            weather_type_string.append('多云')
        elif weather_type[i] == 2:
            weather_type_string.append('晴天')
        elif weather_type[i] == 3:
            weather_type_string.append('多云转晴')
        else:
            weather_type_string.append('晴转多云')
    
    # 将天气类型数据存储到数据库
    weather_type_data_insert(database=database,
                            user=user,
                            password=password,
                            host=host,
                            port=port,
                            time_cycle=time_cycle,
                            project_id=project_id,
                            project_name=project_name,
                            user_id=user_id,
                            time_flag=time_flag_day,
                            weather_type=weather_type_string)
    
    # 将天气类型评估结果保存到Excel文件
    evaluation_weather_type = {'时间': time_flag_day, '天气类型': weather_type_string}
    df = pd.DataFrame(evaluation_weather_type)
    df.to_excel(PATH_EVA_RESULT1, index=False)

    # ========== 评估指标计算和存储 ==========
    time_flag = time_flag.flatten()
    
    # 计算各项评估指标
    cover_rate = CoverRate(data, predict_up, predict_low)                           # 功率覆盖率
    distance_upandlow_ave, distance_upandlow_max = DistanceUpandLow(predict_up, predict_low)  # 上下界距离
    pinball_ave, pinball_max = Pinball(data, predict_up, predict_low)              # Pinball损失
    ac_pmax_ave, ac_pmax_max = AccuracyPmax(data, predict_mean)                     # 日最大功率误差
    ac_pave_ave, ac_pave_max = AccuracyPave(data, predict_mean)                     # 日平均功率误差
    ac_t80 = AccuracyT80(data, predict_mean)                                       # 80%最大功率生成时间误差
    
    # 将评估指标存储到数据库
    evaluation_data_insert(database=database,
                          user=user,
                          password=password,
                          host=host,
                          port=port,
                          time_cycle=time_cycle,
                          project_id=project_id,
                          project_name=project_name,
                          user_id=user_id,
                          time_flag=time_flag,
                          cover_rate=cover_rate,
                          distance_upandlow_ave=distance_upandlow_ave,
                          distance_upandlow_max=distance_upandlow_max,
                          pinball_ave=pinball_ave,
                          pinball_max=pinball_max,
                          daily_max_power_error_ave=ac_pmax_ave,
                          daily_max_power_error_max=ac_pmax_max,
                          daily_ave_power_error_ave=ac_pave_ave,
                          daily_ave_power_error_max=ac_pave_max,
                          eighty_percent_max_power_generation_time_error=ac_t80)
    
    # 将评估指标保存到Excel文件
    evaluation_indicator = {'功率覆盖率': cover_rate,
                           '上下界距离(ave,max)': [distance_upandlow_ave, distance_upandlow_max],
                           'Pinball(ave,max)': [pinball_ave, pinball_max],
                           '日最大功率误差(ave,max)': [ac_pmax_ave, ac_pmax_max],
                           '日平均功率误差(ave,max)': [ac_pave_ave, ac_pave_max],
                           '80%最大功率生成时间误差': ac_t80}
    df = pd.DataFrame(evaluation_indicator)
    df.to_excel(PATH_EVA_RESULT2, index=False)

    # ========== 仿真结果数据存储 ==========
    # 将多维数组展平为一维，便于存储
    time_flag = time_flag.flatten()
    predict_mean = predict_mean.flatten()
    predict_up = predict_up.flatten()
    predict_low = predict_low.flatten()
    data = data.flatten()
    
    # 将时间序列数据存储到数据库
    time_series_data_insert(database=database,
                           user=user,
                           password=password,
                           host=host,
                           port=port,
                           time_cycle=time_cycle,
                           project_id=project_id,
                           project_name=project_name,
                           user_id=user_id,
                           time_flag=time_flag,
                           predict_mean=predict_mean,
                           predict_up=predict_up,
                           predict_low=predict_low,
                           actual_output=data)
    
    # 将仿真结果保存到Excel文件
    simulation = {'时间': time_flag, 
                 '生成出力': predict_mean, 
                 '出力上界': predict_up, 
                 '出力下界': predict_low, 
                 '实际出力': data}
    df = pd.DataFrame(simulation)
    df.to_excel(PATH_SIM_RESULT, index=False)


# ========== 主程序入口 ==========
if __name__ == "__main__":
    # 运行光伏出力一级输出预测函数
    One_level_output()

