import numpy as np
import torch
from Bayes_bp import BpNetwork
import pandas as pd
import os
from Myloss import My_loss

def Baysian_bp_train(path_power="F:/SanXia/Generate_PV/cluster_first/大兴/power_阴天.csv",
             path_weather="F:/SanXia/Generate_PV/cluster_first/大兴/weather_阴天.csv",
             path_root="F:/SanXia/Generate_PV/train_data/大兴/阴天",
             max_epoch=4500,
             time=1440,
             hidden_size=256,
             batch_size=1,
             epsilon=1e-4,
             lr=1e-3,
             weight_decay=0.005
             ):
    
    """
    :param path_power:功率文件
    :param path_weather:气象文件
    :param path_root:网络参数存储路径
    :param time:日曲线的数据点个数
    :param hidden_size: 神经网络隐藏神经元个数
    :param batch_size: 训练批大小
    :param epsilon: 训练误差限度
    :param lr: 训练梯度下降学习率
    :param weight_decay: 训练权重衰减系数
    :return 该天气类型下Bayesian-Bp网络参数
    """

    INPUT_SIZE = time
    OUTPUT_SIZE = time
    HIDDEN_SIZE = hidden_size
    BATCH_SIZE = batch_size
    MAX_EPOCHS = max_epoch
    EPSILON = epsilon
    LR=lr
    WEIGHT_DECAY=weight_decay
    
    if not os.path.exists(path_root):
        os.makedirs(path_root)
    PATH=os.path.join(path_root, f'bp_model_params.pth')
    
    
    '''Data process'''
    
    power_1 = pd.read_csv(path_power,header=None)
    power_1=power_1.values
    weather_1 = pd.read_csv(path_weather,header=None)
    weather_1=weather_1.values
    power_1=np.array(power_1).reshape(-1,time)
    weather_1=np.array(weather_1).reshape(-1,time)
    
    dataset1=np.hstack((weather_1,power_1))
    dataset1=np.reshape(dataset1,(power_1.shape[0],time*2))
    dataset = dataset1.astype('float32')

    train_x = dataset[:, 0:INPUT_SIZE]
    train_y = dataset[:, INPUT_SIZE:INPUT_SIZE+OUTPUT_SIZE]

    loss_list=[]
    
    '''train'''
    
    train_x_tensor = train_x.reshape(-1, BATCH_SIZE,INPUT_SIZE)  
    train_y_tensor = train_y.reshape(-1, BATCH_SIZE,OUTPUT_SIZE) 
 
    # transfer data to pytorch tensor
    train_x_tensor = torch.from_numpy(train_x_tensor)
    train_y_tensor = torch.from_numpy(train_y_tensor)
 
    bp_model = BpNetwork(n_input=INPUT_SIZE,n_hidden=HIDDEN_SIZE, n_output=OUTPUT_SIZE) 
    print('bp model:', bp_model)
    loss_function = My_loss()
    optimizer = torch.optim.Adam(bp_model.parameters(), lr=LR,weight_decay=WEIGHT_DECAY)
    
    try:
        bp_model.load_state_dict(torch.load(PATH)) 
    except:
        pass
 
    for epoch in range(MAX_EPOCHS):
        optimizer.zero_grad()
        loss = bp_model.sample_elbo_selfdefine(inputs=train_x_tensor,
                                   labels=train_y_tensor,
                                   criterion=loss_function,
                                   sample_nbr=10,
                                   complexity_cost_weight=1./train_x.shape[0])
        loss.backward()
        optimizer.step()
        
 
        if loss.item() < EPSILON:
            print('Epoch [{}/{}], Loss: {:.5f}'.format(epoch+1, MAX_EPOCHS, loss.item()))
            print("The loss value is reached")
            loss_list.append(loss.item())
            break
        elif (epoch+1) % 100 == 0:
            print('Epoch: [{}/{}], Loss:{:.5f}'.format(epoch+1, MAX_EPOCHS, loss.item()))
            loss_list.append(loss.item())

    torch.save( bp_model.state_dict(),PATH) # save model parameters to files
    
