import torch.nn as nn
from blitz.modules import BayesianLinear,BayesianConv1d
from Variational_estimator import variational_estimator

@variational_estimator
class CNNNetwork(nn.Module):
    def __init__(self,n_input,n_hidden,n_output):
        super(CNNNetwork,self).__init__()
        self.fc1 = BayesianLinear(n_input,n_hidden)
        self.conv1=BayesianConv1d(n_hidden,int(n_hidden/2),3, stride=1, padding=1)
        self.norm1=nn.BatchNorm1d(int(n_hidden/2))
        self.conv2=BayesianConv1d(int(n_hidden/2),n_hidden,3, stride=1, padding=1)
        self.norm2=nn.BatchNorm1d(n_hidden)
        self.fc2 = BayesianLinear(n_hidden,n_output)
        self.sigmoid = nn.Sigmoid()
       
    
    def forward(self,input):
        s, b, h = input.shape 
        x = input.view(s*b, h) #变为二维张量
        x=self.fc1(x)
        x = x.view(s,-1,b) #变为二维张量
        x=self.conv1(x)
        x=self.norm1(x)
        x=self.conv2(x)
        x=self.norm2(x)
        x=x.view(s*b,-1)        
        x=self.fc2(x)
        x=self.sigmoid(x)
        output = x.view(s, b, -1) #size (seq_len, batch, output_size)
        return output


